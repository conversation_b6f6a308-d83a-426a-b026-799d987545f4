# Godot Engine Development Style Guide

This document contains a summary of development style guides and best practices for Godot Engine, compiled from the official documentation.

## General Best Practices

- **Object-Oriented Design:** Rely on principles like the Single Responsibility Principle and Encapsulation to structure code and solve problems.
- **Real-World Examples:** Prefer real-world examples over abstract ones (e.g., avoid 'foo', 'bar').
- **Writing Style:** Use active voice, precise action verbs, and avoid unnecessary adverbs/adjectives. Words like 'obvious', 'simple', 'easy', 'just' should be avoided in documentation and comments.

---

## GDScript Style Guide

Inspired by Python's PEP 8. The Godot script editor helps enforce many of these conventions by default.

### Code Organization

The recommended order for elements in a script file:

1.  `@tool`, `@icon`
2.  `class_name`
3.  `extends`
4.  Docstrings (`## ...`)
5.  `signal`
6.  `enum`
7.  `const`
8.  `@export` variables
9.  Public variables
10. Private variables (prefixed with `_`)
11. `@onready` variables
12. `_init()` and other built-in virtual methods (`_ready`, `_process`, etc.)
13. Public methods
14. Private methods (prefixed with `_`)
15. Subclasses

### Naming Conventions

-   **Files:** `snake_case` (e.g., `player_controller.gd`). Convert `PascalCase` class names to `snake_case` for filenames.
-   **Classes/Nodes:** `PascalCase` (e.g., `PlayerController`, `StateMachine`).
-   **Functions & Variables:** `snake_case` (e.g., `load_level`, `particle_effect`).
-   **Private Members:** `_snake_case` (leading underscore) (e.g., `_recalculate_path`, `_counter`).
-   **Constants & Enums:** `CONSTANT_CASE` or `PascalCase` for Enum names, `CONSTANT_CASE` for members (e.g., `const MAX_SPEED = 200`, `enum Element { EARTH, WATER }`).
-   **Signals:** `past_tense` (e.g., `door_opened`, `score_changed`).

### Indentation & Formatting

-   **Indentation:** Use **Tabs**, not spaces.
-   **Line Breaks:** Use blank lines to separate functions and logical blocks inside functions.
-   **Trailing Commas:** Use trailing commas in multi-line arrays and dictionaries for cleaner diffs and easier refactoring.
-   **Whitespace:** Use spaces around operators (`x = y + 5`) and after commas.
-   **Quotes:** Prefer double quotes (`"`). Use single quotes (`'`) only to avoid escaping.

### Static Typing

-   Use static typing for clarity where the type isn't immediately obvious.
-   Use inferred typing (`:=`) when the type is clear from the right-hand side (e.g., `var direction := Vector3.UP`).
-   Avoid redundant type hints (e.g., `var direction: Vector3 = Vector3.UP`).

---
