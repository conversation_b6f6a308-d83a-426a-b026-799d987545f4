### 需求

要為 iOS 進行匯出，您需要：
*   一台運行 macOS 且已安裝 Xcode 的 Mac 電腦。
*   一個 Apple 開發者帳號。
*   Godot 編輯器中的 iOS 匯出範本。

### 匯出流程

以下是將 Godot 專案匯出到 Xcode 的基本步驟：

1.  **安裝匯出範本**：在 Godot 編輯器中，前往「專案」->「匯出...」。如果尚未安裝 iOS 範本，請立即下載並安裝。
2.  **新增 iOS 匯出預設**：在「匯出」視窗中，點擊「新增...」並選擇「iOS」。
3.  **設定必要選項**：
    *   **App Store Team ID**：這是您的 Apple 開發者帳號團隊 ID。 您可以在 Xcode 的帳號設定中找到它，或是在 Keychain Access 中查看您的開發憑證的「Organizational Unit」欄位。
    *   **Bundle Identifier**：這是您應用程式的唯一標識符，通常採用反向域名格式（例如 `com.yourcompany.gamename`）。
4.  **匯出專案**：點擊「匯出專案」，選擇一個空資料夾來存放 Xcode 專案檔案。請勿將其匯出到您的 Godot 專案資料夾中。

### 在 Xcode 中建置與部署

匯出完成後，您會在指定的資料夾中找到一個 `.xcodeproj` 檔案。

1.  **在 Xcode 中開啟專案**：雙擊 `.xcodeproj` 檔案以在 Xcode 中開啟它。
2.  **連接您的 iOS 裝置**：使用 USB 連接線將您的 iPhone 或 iPad 連接到 Mac。您需要在裝置上信任這台電腦。
3.  **設定簽署與功能**：在 Xcode 中，選擇您的專案，然後前往「Signing & Capabilities」標籤頁。確保已選擇您的開發團隊，並讓 Xcode 自動管理簽署。
4.  **建置與執行**：從 Xcode 頂端的選單中選擇您的 iOS 裝置，然後點擊「執行」（播放按鈕）來建置應用程式並將其安裝到您的裝置上。

### 持續開發

為了避免每次修改後都重新匯出整個專案，您可以設定 Xcode 以進行「Active Development」。這允許 Godot 在您執行專案時，將更新後的場景直接同步到 Xcode 專案中，大幅簡化測試流程。

### 無 Mac 的替代方案

雖然官方流程需要一台 Mac，但社群中有人提出使用 GitHub Actions 等雲端 CI/CD 服務來建置 iOS 應用程式的替代方案，這可以讓您在沒有實體 Mac 的情況下完成建置。

這份摘要涵蓋了從 Godot 匯出到在 iOS 裝置上部署的基本流程。如需更深入的細節，例如處理憑證、上架 App Store Connect 等，建議參考官方文件和相關的教學影片。
