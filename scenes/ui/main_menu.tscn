[gd_scene load_steps=3 format=3 uid="uid://bqxmxv1aslr7g"]

[ext_resource type="Script" uid="uid://de66686jtpqpg" path="res://scripts/ui/main_menu.gd" id="1_main_menu"]
[ext_resource type="PackedScene" path="res://scenes/ui/settings_menu.tscn" id="2_settings"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main_menu")

[node name="SettingsMenu" parent="." instance=ExtResource("2_settings")]
anchors_preset = 0

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -100.0
offset_right = 150.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Wavebreaker"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2

[node name="StartGameButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "開始遊戲"

[node name="Spacer2" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="UpgradesButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "升級"

[node name="Spacer3" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "設定"

[node name="Spacer4" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="QuitGameButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "退出遊戲"

[connection signal="pressed" from="VBoxContainer/StartGameButton" to="." method="_on_start_game_button_pressed"]
[connection signal="pressed" from="VBoxContainer/UpgradesButton" to="." method="_on_upgrades_button_pressed"]
[connection signal="pressed" from="VBoxContainer/SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="VBoxContainer/QuitGameButton" to="." method="_on_quit_game_button_pressed"]
