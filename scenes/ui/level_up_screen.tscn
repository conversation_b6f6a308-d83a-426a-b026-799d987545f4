[gd_scene load_steps=2 format=3 uid="uid://d9x0y1z2a3b4c"]

[ext_resource type="Script" path="res://scripts/ui/level_up_screen.gd" id="1_level_up_screen"]

[node name="LevelUpScreen" type="CanvasLayer" groups=["level_up_screen"]]
process_mode = 2
visible = false
script = ExtResource("1_level_up_screen")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="CenterContainer" type="CenterContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="Panel" type="Panel" parent="CenterContainer"]
layout_mode = 2
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="TitleLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "LEVEL UP!"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="DescriptionLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "Choose an upgrade:"
horizontal_alignment = 1

[node name="OptionsContainer" type="VBoxContainer" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Option1Button" type="Button" parent="CenterContainer/Panel/VBoxContainer/OptionsContainer"]
layout_mode = 2
text = "Increase Damage"

[node name="Option2Button" type="Button" parent="CenterContainer/Panel/VBoxContainer/OptionsContainer"]
layout_mode = 2
text = "Increase Fire Rate"

[node name="Option3Button" type="Button" parent="CenterContainer/Panel/VBoxContainer/OptionsContainer"]
layout_mode = 2
text = "Increase Health"

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="ContinueButton" type="Button" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "Continue"
