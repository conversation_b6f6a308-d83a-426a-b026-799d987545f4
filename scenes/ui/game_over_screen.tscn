[gd_scene load_steps=2 format=3 uid="uid://bqxmxv1aslr7i"]

[ext_resource type="Script" uid="uid://dp8p2ilkif3pf" path="res://scripts/ui/game_over_screen.gd" id="1_game_over_screen"]

[node name="GameOverScreen" type="CanvasLayer"]
process_mode = 3
script = ExtResource("1_game_over_screen")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="CenterContainer" type="CenterContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer"]
layout_mode = 2

[node name="GameOverLabel" type="Label" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
text = "遊戲結束"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2

[node name="ReturnToMenuButton" type="Button" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
text = "返回主選單"

[connection signal="pressed" from="CenterContainer/VBoxContainer/ReturnToMenuButton" to="." method="_on_return_to_menu_button_pressed"]
