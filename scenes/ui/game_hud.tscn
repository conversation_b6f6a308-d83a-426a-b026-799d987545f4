[gd_scene load_steps=4 format=3 uid="uid://c8yay001a2b3c"]

[ext_resource type="Script" uid="uid://t2y0uwpyh5g3" path="res://scripts/ui/game_hud.gd" id="1_game_hud"]
[ext_resource type="Script" path="res://scripts/ui/boss_health_bar.gd" id="2_boss_health_bar"]
[ext_resource type="PackedScene" uid="uid://dklf2gq8gq2j4" path="res://scenes/components/health_bar.tscn" id="3_health_bar"]

[node name="GameHUD" type="CanvasLayer"]
script = ExtResource("1_game_hud")

[node name="HUDContainer" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BossHealthBar" type="HBoxContainer" parent="HUDContainer"]
visible = false
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -250.0
offset_top = 20.0
offset_right = 250.0
offset_bottom = 60.0
grow_horizontal = 2
script = ExtResource("2_boss_health_bar")
health_bar_scene = ExtResource("3_health_bar")

[node name="TopLeftPanel" type="Panel" parent="HUDContainer"]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 300.0
offset_bottom = 120.0

[node name="VBoxContainer" type="VBoxContainer" parent="HUDContainer/TopLeftPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="LevelLabel" type="Label" parent="HUDContainer/TopLeftPanel/VBoxContainer"]
layout_mode = 2
text = "Level: 1"
horizontal_alignment = 1

[node name="ExperienceBar" type="ProgressBar" parent="HUDContainer/TopLeftPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ExperienceLabel" type="Label" parent="HUDContainer/TopLeftPanel/VBoxContainer"]
layout_mode = 2
text = "0 / 10 XP"
horizontal_alignment = 1
