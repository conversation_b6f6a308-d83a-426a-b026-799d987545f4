[gd_scene load_steps=2 format=3 uid="uid://dq5eu17nl3d8w"]

[ext_resource type="Script" uid="uid://0j5iq77moj01" path="res://scripts/ui/meta_upgrade_screen.gd" id="1_2vna2"]

[node name="MetaUpgradeScreen" type="CanvasLayer"]
script = ExtResource("1_2vna2")

[node name="Panel" type="Panel" parent="."]
offset_left = 246.0
offset_top = 257.0
offset_right = 491.0
offset_bottom = 524.0

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 0
offset_right = 205.0
offset_bottom = 236.0

[node name="TitleLabel" type="Label" parent="Panel/VBoxContainer"]
layout_mode = 2
text = "永久升級"

[node name="CurrencyLabel" type="Label" parent="Panel/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="HealthLabel" type="Label" parent="Panel/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="SpeedLabel" type="Label" parent="Panel/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="UpgradeHealthButton" type="Button" parent="Panel/VBoxContainer"]
layout_mode = 2
text = "升級生命值 (費用: 100)"

[node name="UpgradeSpeedButton" type="Button" parent="Panel/VBoxContainer"]
layout_mode = 2
text = "升級速度 (費用: 150)"

[node name="BackButton" type="Button" parent="Panel/VBoxContainer"]
layout_mode = 2
text = "返回選單"

[connection signal="pressed" from="Panel/VBoxContainer/UpgradeHealthButton" to="." method="_on_upgrade_health_button_pressed"]
[connection signal="pressed" from="Panel/VBoxContainer/UpgradeSpeedButton" to="." method="_on_upgrade_speed_button_pressed"]
[connection signal="pressed" from="Panel/VBoxContainer/BackButton" to="." method="_on_back_button_pressed"]
