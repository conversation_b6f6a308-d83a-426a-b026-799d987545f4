[gd_scene load_steps=5 format=3 uid="uid://bqx80a01a2b3c"]

[ext_resource type="Script" uid="uid://cyg76ynhlpq2k" path="res://scripts/pickups/experience_orb.gd" id="1_experience_orb"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_icon"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 8.0

[sub_resource type="CircleShape2D" id="CircleShape2D_effect"]
radius = 12.0

[node name="ExperienceOrb" type="Area2D" groups=["experience_orbs"]]
collision_mask = 0
script = ExtResource("1_experience_orb")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.2, 0.8, 1, 1)
scale = Vector2(0.3, 0.3)
texture = ExtResource("2_icon")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="AttractionEffect" type="Node2D" parent="."]
visible = false

[node name="PulseRing" type="Area2D" parent="AttractionEffect"]
collision_layer = 0
collision_mask = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="AttractionEffect/PulseRing"]
shape = SubResource("CircleShape2D_effect")
