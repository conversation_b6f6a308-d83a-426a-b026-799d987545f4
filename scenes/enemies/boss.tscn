[gd_scene load_steps=5 format=3 uid="uid://dqlgq2jbaq5x4"]

[ext_resource type="Script" uid="uid://du0gnf2b4lng5" path="res://scripts/enemies/boss.gd" id="1_m8w6v"]
[ext_resource type="Script" uid="uid://2gm20ui4i8s0" path="res://scripts/components/boss_health_component.gd" id="2_x5dcv"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="3_y7g6t"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_boss_collision"]
size = Vector2(128, 128)

[node name="Boss" type="CharacterBody2D" groups=["boss", "enemy_group"]]
collision_layer = 2
script = ExtResource("1_m8w6v")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2, 2)
texture = ExtResource("3_y7g6t")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_boss_collision")

[node name="AttackTimer" type="Timer" parent="."]
wait_time = 2.0
one_shot = true

[node name="StunTimer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true

[node name="BossHealthComponent" type="Node" parent="."]
script = ExtResource("2_x5dcv")
health_stages = Array[float]([10.0, 20.0, 30.0])

[connection signal="timeout" from="AttackTimer" to="." method="_on_attack_timer_timeout"]
[connection signal="timeout" from="StunTimer" to="." method="_on_stun_timer_timeout"]
