[gd_scene load_steps=7 format=3 uid="uid://b8oxudr1qcsmg"]

[ext_resource type="Script" uid="uid://ccwp321pfexly" path="res://scripts/enemies/enemy.gd" id="1_m3x5k"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_h9r2l"]
[ext_resource type="PackedScene" uid="uid://dlr5bgm3imu3g" path="res://scenes/components/health_component.tscn" id="3_k7m8n"]
[ext_resource type="PackedScene" uid="uid://c8x2m5n4p7q1r" path="res://scenes/components/health_bar.tscn" id="4_x9j6l"]
[ext_resource type="PackedScene" uid="uid://bqx80a01a2b3c" path="res://scenes/pickups/experience_orb.tscn" id="5_experience_orb"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_8kx3m"]
radius = 28.0
height = 56.0

[node name="Enemy" type="CharacterBody2D" groups=["enemy_group"]]
collision_layer = 2
script = ExtResource("1_m3x5k")
experience_orb_scene = ExtResource("5_experience_orb")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0.3, 0.3, 1)
scale = Vector2(0.8, 0.8)
texture = ExtResource("2_h9r2l")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_8kx3m")

[node name="HealthComponent" parent="." instance=ExtResource("3_k7m8n")]

[node name="HealthBar" parent="." instance=ExtResource("4_x9j6l")]

[connection signal="died" from="HealthComponent" to="." method="_on_health_component_died"]
