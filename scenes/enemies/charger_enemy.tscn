[gd_scene load_steps=7 format=3 uid="uid://charger_enemy_scene"]

[ext_resource type="Script" path="res://scripts/enemies/charger_enemy.gd" id="1_charger_script"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_icon"]
[ext_resource type="PackedScene" uid="uid://dlr5bgm3imu3g" path="res://scenes/components/health_component.tscn" id="3_health_component"]
[ext_resource type="PackedScene" uid="uid://c8x2m5n4p7q1r" path="res://scenes/components/health_bar.tscn" id="4_health_bar"]
[ext_resource type="PackedScene" uid="uid://bqx80a01a2b3c" path="res://scenes/pickups/experience_orb.tscn" id="5_experience_orb"]
[ext_resource type="Resource" uid="uid://dszszszszszszs" path="res://resources/enemies/charger_enemy.tres" id="6_charger_data"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_charger"]
radius = 32.0
height = 64.0

[node name="ChargerEnemy" type="CharacterBody2D" groups=["enemy_group", "charger_enemy_group"]]
collision_layer = 2
script = ExtResource("1_charger_script")
enemy_data = ExtResource("6_charger_data")
experience_orb_scene = ExtResource("5_experience_orb")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0.7, 0.3, 1)
scale = Vector2(0.9, 0.9)
texture = ExtResource("2_icon")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_charger")

[node name="HealthComponent" parent="." instance=ExtResource("3_health_component")]

[node name="HealthBar" parent="." instance=ExtResource("4_health_bar")]

[node name="DetectionArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="DetectionCollision" type="CollisionShape2D" parent="DetectionArea"]
shape = SubResource("CapsuleShape2D_charger")

[node name="AttackArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="AttackCollision" type="CollisionShape2D" parent="AttackArea"]
shape = SubResource("CapsuleShape2D_charger")

[node name="HornSprite" type="Sprite2D" parent="."]
modulate = Color(0.8, 0.6, 0.2, 1)
scale = Vector2(0.3, 0.3)
texture = ExtResource("2_icon")
position = Vector2(0, -30)

[connection signal="died" from="HealthComponent" to="." method="_on_health_component_died"]
