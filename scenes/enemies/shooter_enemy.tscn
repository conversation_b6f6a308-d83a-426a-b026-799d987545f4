[gd_scene load_steps=7 format=3 uid="uid://shooter_enemy_scene"]

[ext_resource type="Script" path="res://scripts/enemies/shooter_enemy.gd" id="1_shooter_script"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_icon"]
[ext_resource type="PackedScene" uid="uid://dlr5bgm3imu3g" path="res://scenes/components/health_component.tscn" id="3_health_component"]
[ext_resource type="PackedScene" uid="uid://c8x2m5n4p7q1r" path="res://scenes/components/health_bar.tscn" id="4_health_bar"]
[ext_resource type="PackedScene" uid="uid://bqx80a01a2b3c" path="res://scenes/pickups/experience_orb.tscn" id="5_experience_orb"]
[ext_resource type="Resource" uid="uid://cryryryryryryr" path="res://resources/enemies/shooter_enemy.tres" id="6_shooter_data"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_shooter"]
radius = 24.0
height = 48.0

[node name="ShooterEnemy" type="CharacterBody2D" groups=["enemy_group", "shooter_enemy_group"]]
collision_layer = 2
script = ExtResource("1_shooter_script")
enemy_data = ExtResource("6_shooter_data")
experience_orb_scene = ExtResource("5_experience_orb")
projectile_scene = ExtResource("5_experience_orb")  # 將在運行時設置為正確的投射物場景

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.3, 0.3, 1, 1)
scale = Vector2(0.7, 0.7)
texture = ExtResource("2_icon")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_shooter")

[node name="HealthComponent" parent="." instance=ExtResource("3_health_component")]

[node name="HealthBar" parent="." instance=ExtResource("4_health_bar")]

[node name="DetectionArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="DetectionCollision" type="CollisionShape2D" parent="DetectionArea"]
shape = SubResource("CapsuleShape2D_shooter")

[node name="AttackArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="AttackCollision" type="CollisionShape2D" parent="AttackArea"]
shape = SubResource("CapsuleShape2D_shooter")

[node name="GunSprite" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.5, 0.5, 1)
scale = Vector2(0.4, 0.4)
texture = ExtResource("2_icon")
position = Vector2(0, -20)

[connection signal="died" from="HealthComponent" to="." method="_on_health_component_died"]
