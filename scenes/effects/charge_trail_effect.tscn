[gd_scene load_steps=3 format=3 uid="uid://bqxvn8yqxqxqx"]

[ext_resource type="Script" path="res://scripts/effects/charge_trail_effect.gd" id="1_0xqxq"]

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(1, 0.5, 0, 1, 1, 0.8, 0.2, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_1"]
gradient = SubResource("Gradient_1")
width = 64
height = 64

[node name="ChargeTrailEffect" type="Node2D"]
script = ExtResource("1_0xqxq")

[node name="TrailParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 20
lifetime = 0.8
one_shot = true
explosiveness = 0.2
emission_shape = 1
emission_sphere_radius = 8.0
direction = Vector2(0, 0)
spread = 180.0
gravity = Vector2(0, 0)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
scale_amount_min = 0.5
scale_amount_max = 1.5
color_ramp = SubResource("GradientTexture2D_1")

[node name="TrailSprite" type="Sprite2D" parent="."]
texture = SubResource("GradientTexture2D_1")
modulate = Color(1, 0.5, 0, 0.6)
scale = Vector2(0.5, 0.5)
