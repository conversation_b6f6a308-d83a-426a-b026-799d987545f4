[gd_scene load_steps=3 format=3 uid="uid://bu2y3gq18v1vj"]

[ext_resource type="Script" path="res://scripts/effects/charge_impact_effect.gd" id="1_0xqxq"]

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(1, 0.8, 0, 1, 1, 0.4, 0, 0.8, 0.5, 0.2, 0, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_1"]
gradient = SubResource("Gradient_1")
width = 96
height = 96

[node name="ChargeImpactEffect" type="Node2D"]
script = ExtResource("1_0xqxq")

[node name="ImpactParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 50
lifetime = 0.6
one_shot = true
explosiveness = 0.4
emission_shape = 1
emission_sphere_radius = 24.0
direction = Vector2(0, 0)
spread = 360.0
gravity = Vector2(0, 0)
initial_velocity_min = 80.0
initial_velocity_max = 150.0
scale_amount_min = 0.6
scale_amount_max = 1.4
color_ramp = SubResource("GradientTexture2D_1")

[node name="ImpactSprite" type="Sprite2D" parent="."]
texture = SubResource("GradientTexture2D_1")
modulate = Color(1, 0.8, 0, 0.8)
scale = Vector2(0.8, 0.8)

[node name="LifetimeTimer" type="Timer" parent="."]
wait_time = 0.6
one_shot = true

[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
