[gd_scene load_steps=4 format=3 uid="uid://bqxvhqjqhqhqh"]

[ext_resource type="Script" path="res://scripts/effects/hit_effect.gd" id="1_1a2b3"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_1"]
direction = Vector3(0, -1, 0)
initial_velocity_min = 50.0
initial_velocity_max = 150.0
angular_velocity_min = -180.0
angular_velocity_max = 180.0
gravity = Vector3(0, 98, 0)
scale_min = 0.5
scale_max = 1.5
color = Color(1, 0.8, 0.2, 1)

[sub_resource type="Gradient" id="Gradient_1"]
offsets = PackedFloat32Array(0, 0.5, 1)
colors = PackedColorArray(1, 1, 1, 1, 1, 0.8, 0.2, 0.8, 1, 0.5, 0, 0)

[node name="HitEffect" type="Node2D"]
script = ExtResource("1_1a2b3")

[node name="HitParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 20
process_material = SubResource("ParticleProcessMaterial_1")
texture = preload("res://icon.svg")
lifetime = 0.8
one_shot = true
explosiveness = 1.0
