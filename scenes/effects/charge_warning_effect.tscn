[gd_scene load_steps=3 format=3 uid="uid://dkeu86pwxj3t8"]

[ext_resource type="Script" path="res://scripts/effects/charge_warning_effect.gd" id="1_0xqxq"]

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(1, 0, 0, 1, 1, 0.5, 0, 0.8)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_1"]
gradient = SubResource("Gradient_1")
width = 128
height = 128

[node name="ChargeWarningEffect" type="Node2D"]
script = ExtResource("1_0xqxq")

[node name="WarningParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 30
lifetime = 0.5
one_shot = true
explosiveness = 0.3
emission_shape = 1
emission_sphere_radius = 16.0
direction = Vector2(0, 0)
spread = 360.0
gravity = Vector2(0, 0)
initial_velocity_min = 20.0
initial_velocity_max = 60.0
scale_amount_min = 0.8
scale_amount_max = 1.2
color_ramp = SubResource("GradientTexture2D_1")

[node name="WarningSprite" type="Sprite2D" parent="."]
texture = SubResource("GradientTexture2D_1")
modulate = Color(1, 0, 0, 0.7)
scale = Vector2(1, 1)

[node name="WarningAnimation" type="AnimationPlayer" parent="."]

[node name="LifetimeTimer" type="Timer" parent="."]
wait_time = 0.5
one_shot = true

[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
