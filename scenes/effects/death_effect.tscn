[gd_scene load_steps=4 format=3 uid="uid://cqxvhqjqhqhqi"]

[ext_resource type="Script" path="res://scripts/effects/death_effect.gd" id="1_2b3c4"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_2"]
direction = Vector3(0, -1, 0)
spread = 45.0
initial_velocity_min = 80.0
initial_velocity_max = 200.0
angular_velocity_min = -360.0
angular_velocity_max = 360.0
gravity = Vector3(0, 150, 0)
scale_min = 0.8
scale_max = 2.0
color = Color(1, 0.4, 0.1, 1)

[sub_resource type="Gradient" id="Gradient_2"]
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)
colors = PackedColorArray(1, 1, 0.8, 1, 1, 0.6, 0.2, 0.9, 0.8, 0.2, 0.1, 0.6, 0.5, 0.1, 0.05, 0)

[node name="DeathEffect" type="Node2D"]
script = ExtResource("1_2b3c4")

[node name="ExplosionParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 50
process_material = SubResource("ParticleProcessMaterial_2")
texture = preload("res://icon.svg")
lifetime = 1.5
one_shot = true
explosiveness = 1.0

[node name="SmokeParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 30
process_material = SubResource("ParticleProcessMaterial_2")
texture = preload("res://icon.svg")
lifetime = 2.0
one_shot = true
explosiveness = 0.8
