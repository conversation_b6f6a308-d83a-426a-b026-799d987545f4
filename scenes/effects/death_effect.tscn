[gd_scene load_steps=4 format=3 uid="uid://cqxvhqjqhqhqi"]

[ext_resource type="Script" uid="uid://nx576rtfk27o" path="res://scripts/effects/death_effect.gd" id="1_2b3c4"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_explosion"]
direction = Vector3(0, -1, 0)
initial_velocity_min = 80.0
initial_velocity_max = 200.0
angular_velocity_min = -360.0
angular_velocity_max = 360.0
gravity = Vector3(0, 150, 0)
scale_min = 0.8
scale_max = 2.0
color = Color(1, 0.4, 0.1, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_smoke"]
direction = Vector3(0, -1, 0)
spread = 30.0
initial_velocity_min = 40.0
initial_velocity_max = 100.0
angular_velocity_min = -180.0
angular_velocity_max = 180.0
gravity = Vector3(0, -20, 0)
scale_max = 1.8
color = Color(0.5, 0.5, 0.5, 0.8)

[node name="DeathEffect" type="Node2D"]
script = ExtResource("1_2b3c4")

[node name="ExplosionParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 50
lifetime = 1.5
one_shot = true
explosiveness = 1.0
process_material = SubResource("ParticleProcessMaterial_explosion")

[node name="SmokeParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 30
lifetime = 2.0
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_smoke")
