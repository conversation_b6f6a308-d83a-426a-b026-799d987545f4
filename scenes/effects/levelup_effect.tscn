[gd_scene load_steps=4 format=3 uid="uid://dqxvhqjqhqhqj"]

[ext_resource type="Script" path="res://scripts/effects/levelup_effect.gd" id="1_3c4d5"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_3"]
direction = Vector3(0, -1, 0)
spread = 30.0
initial_velocity_min = 100.0
initial_velocity_max = 250.0
angular_velocity_min = -90.0
angular_velocity_max = 90.0
gravity = Vector3(0, -50, 0)
scale_min = 1.0
scale_max = 2.5
color = Color(0.2, 0.8, 1, 1)

[sub_resource type="Gradient" id="Gradient_3"]
offsets = PackedFloat32Array(0, 0.4, 0.8, 1)
colors = PackedColorArray(0.8, 1, 1, 1, 0.4, 0.8, 1, 0.9, 0.2, 0.6, 0.9, 0.6, 0.1, 0.4, 0.7, 0)

[node name="LevelupEffect" type="Node2D"]
script = ExtResource("1_3c4d5")

[node name="AuraParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 80
process_material = SubResource("ParticleProcessMaterial_3")
texture = preload("res://icon.svg")
lifetime = 3.0
one_shot = true
explosiveness = 0.3

[node name="SparkleParticles" type="GPUParticles2D" parent="."]
emitting = false
amount = 40
process_material = SubResource("ParticleProcessMaterial_3")
texture = preload("res://icon.svg")
lifetime = 2.5
one_shot = true
explosiveness = 0.5
