[gd_scene load_steps=8 format=3 uid="uid://test_scene"]

[ext_resource type="Script" path="res://scripts/main.gd" id="1_main_script"]
[ext_resource type="PackedScene" uid="uid://player_scene" path="res://scenes/player/player.tscn" id="2_player"]
[ext_resource type="PackedScene" uid="uid://game_hud_scene" path="res://scenes/ui/game_hud.tscn" id="3_game_hud"]
[ext_resource type="PackedScene" uid="uid://level_up_screen_scene" path="res://scenes/ui/level_up_screen.tscn" id="4_level_up_screen"]
[ext_resource type="PackedScene" uid="uid://game_over_screen_scene" path="res://scenes/ui/game_over_screen.tscn" id="5_game_over_screen"]
[ext_resource type="PackedScene" uid="uid://weapon_manager_scene" path="res://scenes/weapons/weapon_manager.tscn" id="6_weapon_manager"]
[ext_resource type="Script" path="res://scripts/enemy_spawner.gd" id="7_enemy_spawner_script"]

[node name="TestScene" type="Node2D"]
script = ExtResource("1_main_script")

[node name="Player" parent="." instance=ExtResource("2_player")]
position = Vector2(960, 540)

[node name="GameHUD" parent="." instance=ExtResource("3_game_hud")]

[node name="LevelUpScreen" parent="." instance=ExtResource("4_level_up_screen")]
visible = false

[node name="GameOverScreen" parent="." instance=ExtResource("5_game_over_screen")]
visible = false

[node name="WeaponManager" parent="." instance=ExtResource("6_weapon_manager")]

[node name="EnemySpawner" type="Node2D" parent="."]
script = ExtResource("7_enemy_spawner_script")
spawn_radius = 300.0
max_enemies = 15
spawn_interval = 2.0

[node name="Camera2D" type="Camera2D" parent="Player"]
enabled = true
current = true

[node name="Background" type="ColorRect" parent="."]
offset_right = 1920.0
offset_bottom = 1080.0
color = Color(0.1, 0.1, 0.15, 1)

[node name="Instructions" type="Label" parent="."]
anchors_preset = 2
anchor_top = 0.0
anchor_bottom = 0.0
offset_left = 20.0
offset_right = 400.0
offset_bottom = 1080.0
text = "測試多樣化敵人系統

控制：
- WASD: 移動
- 滑鼠: 瞄準
- 左鍵: 射擊

敵人類型：
- 紅色: 追蹤型（近戰）
- 藍色: 射擊型（遠程）
- 橙色: 衝鋒型（衝鋒攻擊）

觀察不同敵人的AI行為！"
label_settings = SubResource("LabelSettings_instructions")

[node name="LabelSettings" type="LabelSettings" parent="Instructions"]
font_size = 18
font_color = Color(1, 1, 1, 0.8)
