[gd_scene load_steps=11 format=3 uid="uid://bx8mxv1aslr7g"]

[ext_resource type="Script" uid="uid://bil2mnwu6govi" path="res://scripts/player/player.gd" id="1_w7m8a"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_4xmx5"]
[ext_resource type="Script" uid="uid://dhtwxxm0mgr73" path="res://scripts/weapons/magic_missile_weapon.gd" id="3_k9x2l"]
[ext_resource type="PackedScene" uid="uid://bm4x8dk5qwt2p" path="res://scenes/weapons/projectile.tscn" id="4_m7j5n"]
[ext_resource type="PackedScene" uid="uid://dlr5bgm3imu3g" path="res://scenes/components/health_component.tscn" id="5_h8k3m"]
[ext_resource type="PackedScene" uid="uid://c8x2m5n4p7q1r" path="res://scenes/components/health_bar.tscn" id="6_j9l2n"]
[ext_resource type="Resource" path="res://resources/player_stats.tres" id="7_0"]
[ext_resource type="Script" uid="uid://c80ak606o7oap" path="res://scripts/managers/weapon_manager.gd" id="8_0"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_1kryb"]
radius = 32.0
height = 64.0

[sub_resource type="CircleShape2D" id="CircleShape2D_pickup"]
radius = 50.0

[node name="Player" type="CharacterBody2D" groups=["player_group"]]
collision_mask = 2
script = ExtResource("1_w7m8a")
stats = ExtResource("7_0")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_4xmx5")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_1kryb")

[node name="WeaponMount" type="Node2D" parent="."]

[node name="MagicMissileWeapon" type="Node" parent="WeaponMount"]
script = ExtResource("3_k9x2l")
projectile_scene = ExtResource("4_m7j5n")

[node name="WeaponManager" type="Node" parent="."]
script = ExtResource("8_0")

[node name="PickupArea" type="Area2D" parent="."]
collision_layer = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="PickupArea"]
shape = SubResource("CircleShape2D_pickup")

[node name="HealthComponent" parent="." instance=ExtResource("5_h8k3m")]
max_health = 50.0

[node name="HealthBar" parent="." instance=ExtResource("6_j9l2n")]

[connection signal="died" from="HealthComponent" to="." method="_on_health_component_died"]
