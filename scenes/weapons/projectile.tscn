[gd_scene load_steps=4 format=3 uid="uid://bm4x8dk5qwt2p"]

[ext_resource type="Script" uid="uid://cd0ygjo6rc6ff" path="res://scripts/weapons/projectile.gd" id="1_r8x3k"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_9j5m4"]

[sub_resource type="CircleShape2D" id="CircleShape2D_7k4x2"]
radius = 8.0

[node name="Projectile" type="Area2D"]
collision_layer = 3
collision_mask = 2
script = ExtResource("1_r8x3k")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.2, 0.8, 1, 1)
scale = Vector2(0.2, 0.2)
texture = ExtResource("2_9j5m4")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_7k4x2")
