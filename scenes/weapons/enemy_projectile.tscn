[gd_scene load_steps=4 format=3 uid="uid://enemy_projectile_scene"]

[ext_resource type="Script" path="res://scripts/weapons/enemy_projectile.gd" id="1_enemy_projectile_script"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_icon"]

[sub_resource type="CircleShape2D" id="CircleShape2D_enemy_projectile"]
radius = 8.0

[node name="EnemyProjectile" type="Area2D" groups=["enemy_projectile_group"]]
collision_layer = 2
collision_mask = 1
script = ExtResource("1_enemy_projectile_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0.2, 0.2, 1)
scale = Vector2(0.3, 0.3)
texture = ExtResource("2_icon")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_enemy_projectile")

[node name="LifetimeTimer" type="Timer" parent="."]
wait_time = 5.0
autostart = true

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
