[gd_scene load_steps=4 format=3 uid="uid://bqxvxqxqxqxqx"]

[ext_resource type="Script" path="res://scripts/weapons/piercing_beam_projectile.gd" id="1_0x0x0"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_0x0x0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(20, 160)

[node name="PiercingBeamProjectile" type="Area2D"]
collision_layer = 2
collision_mask = 1
script = ExtResource("1_0x0x0")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_0x0x0")
modulate = Color(1, 0.8, 0.2, 0.9)
scale = Vector2(0.1, 0.8)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="LifeTimer" type="Timer" parent="."]
wait_time = 3.0
autostart = true

[node name="VisibilityNotifier2D" type="VisibleOnScreenNotifier2D" parent="."]
