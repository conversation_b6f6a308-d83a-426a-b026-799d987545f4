[gd_scene load_steps=4 format=3 uid="uid://dxpmoo422y7va"]

[ext_resource type="Script" path="res://scripts/weapons/guardian_shield.gd" id="1_0x0x0"]
[ext_resource type="Texture2D" uid="uid://lyof65pprvyl" path="res://icon.svg" id="2_0x0x0"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 50.0

[node name="GuardianShield" type="Node2D"]
script = ExtResource("1_0x0x0")

[node name="Shield" type="Area2D" parent="."]
collision_layer = 2
collision_mask = 1

[node name="Sprite2D" type="Sprite2D" parent="Shield"]
texture = ExtResource("2_0x0x0")
modulate = Color(0.2, 0.6, 1, 0.8)
scale = Vector2(0.5, 0.5)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Shield"]
shape = SubResource("CircleShape2D_1")

[node name="DamageTimer" type="Timer" parent="Shield"]
wait_time = 0.5
autostart = false
