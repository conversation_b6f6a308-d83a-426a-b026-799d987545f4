[gd_scene load_steps=10 format=3 uid="uid://bqxmxv1aslr7h"]

[ext_resource type="PackedScene" uid="uid://bx8mxv1aslr7g" path="res://scenes/player/player.tscn" id="1_b4x7r"]
[ext_resource type="Script" uid="uid://cvl2bwud1js8a" path="res://scripts/enemy_spawner.gd" id="2_x8k4m"]
[ext_resource type="PackedScene" uid="uid://dqlgq2jbaq5x4" path="res://scenes/enemies/boss.tscn" id="4_47dfi"]
[ext_resource type="Script" uid="uid://d0kdik7aeeb03" path="res://scripts/worlds/game_world.gd" id="4_game_world"]
[ext_resource type="PackedScene" uid="uid://c8yay001a2b3c" path="res://scenes/ui/game_hud.tscn" id="5_game_hud"]
[ext_resource type="PackedScene" uid="uid://fj5ey3spvgdk" path="res://scenes/ui/level_up_screen.tscn" id="6_level_up_screen"]
[ext_resource type="Resource" uid="uid://bqxqxqxqxqxqx" path="res://resources/enemies/tracker_enemy.tres" id="7_tracker_data"]
[ext_resource type="Resource" uid="uid://xstsqkgdjjbx" path="res://resources/enemies/shooter_enemy.tres" id="8_shooter_data"]
[ext_resource type="Resource" uid="uid://651jab7bqoi3" path="res://resources/enemies/charger_enemy.tres" id="9_charger_data"]

[node name="GameWorld" type="Node2D"]
script = ExtResource("4_game_world")

[node name="Player" parent="." instance=ExtResource("1_b4x7r")]

[node name="EnemySpawner" type="Node2D" parent="."]
script = ExtResource("2_x8k4m")
boss_scene = ExtResource("4_47dfi")
enemy_data_resources = {
"charger": ExtResource("9_charger_data"),
"shooter": ExtResource("8_shooter_data"),
"tracker": ExtResource("7_tracker_data")
}

[node name="WaveTimer" type="Timer" parent="."]
autostart = true

[node name="GameHUD" parent="." instance=ExtResource("5_game_hud")]

[node name="LevelUpScreen" parent="." instance=ExtResource("6_level_up_screen")]

[node name="BossSpawnTestTimer" type="Timer" parent="."]
wait_time = 5.0
one_shot = true

[connection signal="timeout" from="WaveTimer" to="EnemySpawner" method="_on_wave_timer_timeout"]
[connection signal="timeout" from="BossSpawnTestTimer" to="." method="_on_boss_spawn_test_timer_timeout"]
