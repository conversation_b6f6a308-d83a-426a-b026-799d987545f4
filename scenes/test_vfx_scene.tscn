[gd_scene load_steps=2 format=3 uid="uid://test_vfx_scene"]

[ext_resource type="Script" path="res://scripts/test_vfx.gd" id="1_test_vfx"]

[node name="TestVFXScene" type="Node2D"]
script = ExtResource("1_test_vfx")

[node name="Background" type="ColorRect" parent="."]
offset_right = 1920.0
offset_bottom = 1080.0
color = Color(0.1, 0.1, 0.15, 1)

[node name="Instructions" type="Label" parent="."]
anchors_preset = 2
anchor_top = 0.0
anchor_bottom = 0.0
offset_left = 20.0
offset_right = 600.0
offset_bottom = 200.0
text = "VFX 系統測試場景

控制：
- 空白鍵: 重新運行自動測試
- 1: 在滑鼠位置播放擊中特效
- 2: 在滑鼠位置播放死亡特效  
- 3: 在滑鼠位置播放升級特效

檢查控制台輸出以查看測試結果。"
label_settings = SubResource("LabelSettings_instructions")

[sub_resource type="LabelSettings" id="LabelSettings_instructions"]
font_size = 18
font_color = Color(1, 1, 1, 0.8)
