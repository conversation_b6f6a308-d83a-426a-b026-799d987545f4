# Wavebreaker - 開發計畫

本文檔定義了 **Wavebreaker** 的開發路線圖，將開發過程分解為三個主要版本。每個版本都包含一系列按順序排列的任務 (TASK)。

## 版本 1.0: 最小可行產品 (MVP)

目標：完成一個可玩的、包含核心遊戲循環的基礎版本。

---

### **TASK001: 專案設定與基礎玩家**
*   **版本:** 1.0
*   **狀態:** 完成
*   **描述:** 初始化 Godot 專案，建立資料夾結構，並實現一個可以響應輸入並在遊戲世界中移動的基礎玩家角色。

*   **子任務清單:**
    1.  `SUB_001.1`: 建立標準化的專案資料夾結構。
    2.  `SUB_001.2`: 創建 `player` 場景，包含基礎節點 (`CharacterBody2D`, `Sprite2D`, `CollisionShape2D`)。
    3.  `SUB_001.3`: 編寫 `player.gd` 腳本，實現八向移動邏輯。
    4.  `SUB_001.4`: 設置輸入映射 (Input Map) 以處理鍵盤和手把控制。
    5.  `SUB_001.5`: 創建一個簡單的 `main` 或 `world` 場景，並將玩家實例化於其中進行測試。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK001`。

    **目標:** 創建一個基礎的、可移動的玩家角色。

    **詳細要求:**
    1.  **資料夾結構:** 在專案根目錄下創建以下資料夾：`scenes`, `scripts`, `assets`, `scenes/player`, `scripts/player`。
    2.  **Player 場景:**
        *   創建一個名為 `player.tscn` 的新場景。
        *   根節點為 `CharacterBody2D`，命名為 `Player`。
        *   添加一個 `Sprite2D` 節點用於顯示臨時圖像 (可使用 Godot 圖標 `icon.svg`)。
        *   添加一個 `CollisionShape2D` 節點，並為其分配一個 `CapsuleShape2D` 或 `RectangleShape2D` 資源。
    3.  **Player 腳本:**
        *   創建一個名為 `player.gd` 的新腳本，並將其附加到 `Player` 節點。
        *   腳本必須繼承自 `CharacterBody2D`。
        *   嚴格遵守 `docs/GODOT_STYLE_GUIDE.md` 的所有規範 (命名、格式化、註釋等)。
        *   在腳本頂部定義一個 `move_speed` 導出變數 (`@export var move_speed: float = 300.0`)。
        *   在 `_physics_process(delta)` 函數中：
            *   讀取 "move_left", "move_right", "move_up", "move_down" 輸入動作。
            *   計算歸一化 (normalized) 的方向向量。
            *   根據方向和 `move_speed` 設置 `velocity`。
            *   調用 `move_and_slide()` 應用移動。
    4.  **輸入映射:**
        *   請提供一個說明，指導如何在 Godot 編輯器的 "專案設置" -> "輸入映射" 中，為上述四個動作（"move_left" 等）分別綁定 `A`, `D`, `W`, `S` 鍵和方向鍵。
    5.  **測試場景:**
        *   創建一個 `main.tscn`，將 `player.tscn` 實例化到其中，以便運行和測試。

    請為需要創建或修改的文件提供完整的代碼。
    ```

*   **驗收標準:**
    - [x] 專案中存在 `scenes`, `scripts`, `assets` 資料夾。
    - [x] `scenes/player/player.tscn` 場景已創建且結構正確。
    - [x] `scripts/player/player.gd` 腳本已創建並附加到玩家。
    - [x] 運行 `main.tscn` 場景後，可以使用 `W/A/S/D` 和方向鍵控制玩家角色在螢幕上移動。
    - [x] 玩家移動速度可以透過 Inspector（屬性檢視器）面板中的 `move_speed` 變數進行調整。
    - [x] 所有代碼和命名均符合 `GODOT_STYLE_GUIDE.md`。

*   **注意事項:**
    *   `player.gd` 中的移動邏輯應處理歸一化，以防止對角線移動速度過快。
    *   臨時使用的 `icon.svg` 應在後續任務中被正式的美術資源替換。

---

### **TASK002: 簡單敵人與波次生成**
*   **版本:** 1.0
*   **狀態:** 完成
*   **描述:** 創建一個會追蹤玩家的基礎敵人，並建立一個隨時間生成敵人的系統。

*   **子任務清單:**
    1.  `SUB_002.1`: 創建 `enemy` 場景 (`CharacterBody2D`) 和 `enemy.gd` 腳本。
    2.  `SUB_002.2`: 在 `enemy.gd` 中實現追蹤玩家的 AI 邏輯。
    3.  `SUB_002.3`: 創建 `enemy_spawner.gd` 腳本，負責在指定位置實例化敵人。
    4.  `SUB_002.4`: 在 `main` 場景中，建立一個計時器，隨時間推移調用 `enemy_spawner` 來生成敵人。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK002`。

    **目標:** 創建一個會追蹤玩家的基礎敵人，並實現一個簡單的波次生成器。

    **先決條件:**
    *   一個可運作的 `player.tscn` 場景和 `player.gd` 腳本 (來自 TASK001)。
    *   玩家節點在場景樹中，或者可以被全局訪問 (例如，將其添加到一個名為 "player_group" 的組中)。

    **詳細要求:**
    1.  **Enemy 場景:**
        *   在 `scenes/enemies/` 中創建 `enemy.tscn`。
        *   根節點為 `CharacterBody2D`，命名為 `Enemy`。
        *   添加 `Sprite2D` (可使用 `icon.svg` 並將其色調調整為紅色以區分) 和 `CollisionShape2D`。
    2.  **Enemy 腳本 (`enemy.gd`):**
        *   附加到 `Enemy` 節點。
        *   定義 `move_speed` 變數。
        *   在 `_physics_process` 中：
            *   需要找到玩家的位置。你可以透過 `get_tree().get_first_node_in_group("player_group")` 來獲取玩家節點。請確保在 `player.tscn` 中將玩家添加到該組。
            *   如果找到了玩家，計算從敵人到玩家的歸一化方向向量。
            *   使用該方向向量和 `move_speed` 來設置 `velocity` 並調用 `move_and_slide()`。
    3.  **Enemy Spawner 邏輯:**
        *   在 `main.tscn` 中添加一個 `Node2D` 節點，命名為 `EnemySpawner`。
        *   為其附加一個新腳本 `enemy_spawner.gd`。
        *   在 `enemy_spawner.gd` 中：
            *   導出一個 `enemy_scene: PackedScene` 變數，用於在 Inspector（屬性檢視器）中指定 `enemy.tscn`。
            *   創建一個 `spawn_enemy()` 方法。此方法應在螢幕可視範圍之外的一個隨機位置實例化 `enemy_scene`，並將其作為子節點添加到 `main` 場景中。
    4.  **波次計時器:**
        *   在 `main.tscn` 中添加一個 `Timer` 節點，命名為 `WaveTimer`。
        *   將其 `autostart` 設置為 true，`wait_time` 設置為 (例如) 1 秒，並將其 `timeout()` 信號連接到 `EnemySpawner` 節點上的一個新方法，例如 `_on_wave_timer_timeout()`。
        *   在 `_on_wave_timer_timeout()` 方法中，調用 `spawn_enemy()`。

    請為所有新創建的腳本提供完整代碼，並說明需要在 Godot 編輯器中進行的節點連接和設置。
    ```

*   **驗收標準:**
    - [x] `enemy.tscn` 和 `enemy.gd` 已創建。
    - [x] 運行遊戲後，敵人會自動生成在螢幕邊緣。
    - [x] 生成的敵人會朝著玩家的方向移動。
    - [x] 玩家可以與敵人發生碰撞。
    - [x] `WaveTimer` 會持續生成新的敵人。

*   **注意事項:**
    *   敵人生成的位置應在螢幕可視範圍之外，以避免突然出現。可以使用 `Viewport` 的大小來計算生成點。
    *   目前的敵人 AI 非常基礎，後續任務將對其進行擴展。
    *   為了性能，當敵人離玩家太遠時，可以考慮禁用其處理 (`set_physics_process(false)`)。

---
### **TASK003: 基礎武器系統 (1種武器)**
*   **版本:** 1.0
*   **狀態:** 完成
*   **描述:** 實現一個模組化的武器系統，並為玩家添加第一種可用的自動攻擊武器。敵人需要能夠接收傷害並被消滅。

*   **子任務清單:**
    1.  `SUB_003.1`: 創建一個可重用的 `HealthComponent` 場景/腳本，用於處理任何角色的生命值、受傷和死亡邏輯。
    2.  `SUB_003.2`: 將 `HealthComponent` 添加到 `enemy.tscn` 中，並實現敵人死亡時的 `queue_free()`。
    3.  `SUB_003.3`: 創建一個基礎的 `projectile.tscn` (`Area2D`)，它能夠在擊中敵人時發出信號。
    4.  `SUB_003.4`: 創建一個 `weapon.gd` 基礎武器腳本，包含冷卻計時器和開火邏輯。
    5.  `SUB_003.5`: 創建第一種具體武器 `magic_missile_weapon.tscn`，它使用 `weapon.gd` 並定時發射 `projectile.tscn`。
    6.  `SUB_003.6`: 在 `player.tscn` 中添加一個 `WeaponManager` 節點，負責持有和觸發當前擁有的武器。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK003`。

    **目標:** 建立一個基礎的、模組化的武器和傷害系統。玩家將自動發射投射物，對敵人造成傷害並消滅它們。

    **先決條件:**
    *   一個可移動的 `Player` (TASK001)。
    *   一個會追蹤玩家的 `Enemy` (TASK002)。

    **詳細要求:**
    1.  **HealthComponent (健康組件):**
        *   在 `scenes/components/` 中創建一個新場景 `health_component.tscn`，根節點為 `Node`。
        *   為其附加腳本 `health_component.gd`。
        *   腳本中應包含：
            *   `@export var max_health: float = 10.0`
            *   一個 `current_health` 變數，在 `_ready()` 中初始化為 `max_health`。
            *   一個 `take_damage(amount: float)` 方法，該方法會減少 `current_health`。如果生命值小於等於 0，則發出一個 `died` 信號。
            *   定義 `signal died`。
    2.  **整合到敵人:**
        *   在 `enemy.tscn` 中實例化 `health_component.tscn`。
        *   將 `HealthComponent` 的 `died` 信號連接到 `Enemy` 節點的主腳本 `enemy.gd` 中的一個新方法，例如 `_on_health_component_died()`。
        *   在 `_on_health_component_died()` 方法中，調用 `queue_free()` 來移除敵人。
    3.  **Projectile (投射物) 場景:**
        *   在 `scenes/weapons/` 中創建 `projectile.tscn`。
        *   根節點為 `Area2D`。添加 `Sprite2D` 和 `CollisionShape2D`。
        *   為其附加腳本 `projectile.gd`。
        *   在腳本中定義 `damage: float = 5.0` 和 `speed: float = 500.0`。
        *   在 `_physics_process` 中，使其沿 `transform.x` (正前方) 移動。
        *   將 `Area2D` 的 `body_entered` 信號連接到自身。在處理方法 `_on_body_entered(body)` 中：
            *   檢查 `body` 是否有 `HealthComponent` (使用 `body.find_child("HealthComponent")`)。
            *   如果有，調用其 `take_damage(damage)` 方法。
            *   無論是否擊中，都應在擊中物體後銷毀投射物 (`queue_free()`)。
    4.  **Weapon (武器) 邏輯:**
        *   在 `player.tscn` 下添加一個 `Node2D` 作為武器的掛載點，命名為 `WeaponMount`。
        *   在 `WeaponMount` 下，添加一個 `Timer` 節點，命名為 `CooldownTimer`。
        *   創建一個 `magic_missile_weapon.gd` 腳本。
        *   此腳本需要：
            *   一個導出的 `projectile_scene: PackedScene`。
            *   一個 `_ready()` 函數，將 `CooldownTimer` 的 `timeout` 信號連接到開火方法。
            *   一個 `fire()` 方法，該方法實例化 `projectile_scene`，將其設置在全局位置，賦予其一個隨機的旋轉角度 (`rotation = randf_range(0, TAU)`)，然後將其添加到場景樹中。
            *   在 `fire()` 後，重新啟動 `CooldownTimer`。
    5.  **整合到玩家:**
        *   在 `player.tscn` 的 `WeaponMount` 下添加一個 `Node`，命名為 `MagicMissileWeapon`，並附加上述的 `magic_missile_weapon.gd` 腳本。
        *   在 Inspector 中，將 `projectile.tscn` 拖入該腳本的 `projectile_scene` 欄位。

    請為所有新創建的腳本提供完整代碼，並說明需要在 Godot 編輯器中進行的節點連接和設置。
    ```

*   **驗收標準:**
    - [x] `health_component.tscn` 已創建並能正確處理傷害和死亡。
    - [x] 敵人被 `HealthComponent` 實例化，並能在生命值耗盡時從場景中移除。
    - [x] 玩家角色會自動、定時地朝隨機方向發射投射物。
    - [x] 投射物擊中敵人時，敵人會受到傷害。
    - [x] 敵人承受足夠傷害後會消失。
    - [ ] 武器的冷卻時間可以透過 `CooldownTimer` 的 `wait_time` 進行調整。

*   **注意事項:**
    *   `HealthComponent` 的設計應是通用的，後續也可以將其添加到玩家身上。
    *   投射物與敵人的碰撞層/遮罩 (Collision Layers/Masks) 需要正確設置，以確保 `Area2D` 的 `body_entered` 信號能被正確觸發。
    *   將武器邏輯與玩家分離是關鍵，這使得未來添加、移除或更換武器變得非常容易。`WeaponManager` 的概念雖然在此任務中被簡化為直接掛載，但為後續擴展奠定了基礎。

---
### **TASK004: 經驗與升級UI**
*   **版本:** 1.0
*   **狀態:** 完成
*   **描述:** 實現經驗系統，讓玩家可以透過擊敗敵人獲得經驗、升級，並彈出一個基礎的升級選擇介面。

*   **子任務清單:**
    1.  `SUB_004.1`: 創建 `experience_orb.tscn`，作為敵人死亡時掉落的經驗拾取物。
    2.  `SUB_004.2`: 修改 `enemy.gd`，使其在死亡時生成 `experience_orb`。
    3.  `SUB_004.3`: 在玩家場景中添加一個拾取範圍 (`Area2D`)，用於偵測並吸引經驗球。
    4.  `SUB_004.4`: 創建 `level_manager.gd` 單例，用於管理玩家的經驗值、等級和升級邏輯。
    5.  `SUB_004.5`: 創建一個基礎的遊戲 HUD，顯示當前等級和經驗條。
    6.  `SUB_004.6`: 創建一個升級畫面 (`CanvasLayer`)，在玩家升級時顯示並暫停遊戲。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK004`。

    **目標:** 建立完整的局內經驗獲取和升級循環，包括數據管理和 UI 顯示。

    **先決條件:**
    *   敵人 (`Enemy`) 能夠被消滅並觸發死亡邏輯 (來自 TASK003)。

    **詳細要求:**
    1.  **ExperienceOrb (經驗球) 場景:**
        *   在 `scenes/pickups/` 中創建 `experience_orb.tscn`。
        *   根節點為 `Area2D`，添加 `Sprite2D` (可使用一個小的、不同顏色的圓形) 和 `CollisionShape2D`。
        *   附加 `experience_orb.gd` 腳本，包含 `@export var experience_amount: int = 1`。
        *   它需要一個 `collect()` 方法，該方法在被收集時發出一個包含 `experience_amount` 的信號 `collected`，然後 `queue_free()`。
    2.  **敵人掉落邏輯:**
        *   在 `enemy.gd` 的 `_on_health_component_died()` 方法中，在 `queue_free()` 之前，實例化 `experience_orb.tscn` 並將其放置在敵人的 `global_position`。
    3.  **玩家拾取範圍:**
        *   在 `player.tscn` 中，添加一個名為 `PickupArea` 的 `Area2D` 子節點。給它一個大的圓形 `CollisionShape2D`。
        *   將 `PickupArea` 的 `area_entered` 信號連接到 `player.gd`。
        *   在 `_on_pickup_area_area_entered(area)` 方法中，檢查 `area` 是否為一個經驗球 (例如，`area.is_in_group("experience_orbs")`)。如果是，調用 `area.collect()`。請確保將 `experience_orb.tscn` 添加到 "experience_orbs" 組中。
    4.  **LevelManager (等級管理器):**
        *   創建一個新腳本 `scripts/managers/level_manager.gd`。
        *   在 "專案設置" -> "自動加載" 中將其註冊為名為 `LevelManager` 的單例。
        *   腳本需要：
            *   信號 `level_up(new_level)` 和 `experience_updated(current_xp, required_xp)`。
            *   變數 `current_level`, `current_xp`。
            *   一個 `xp_for_next_level` 陣列或公式來定義升級所需經驗 (例如 `[10, 20, 30, 50, 80, ...]`)。
            *   一個 `add_experience(amount: int)` 方法。此方法增加 `current_xp`，檢查是否滿足升級條件。如果滿足，則增加 `current_level`，更新 `current_xp` (減去升級所需XP)，並發出 `level_up` 信號。無論是否升級，都發出 `experience_updated` 信號。
    5.  **連接收集與管理:**
        *   在 `player.gd` 中，當 `area.collect()` 被調用後，你需要監聽經驗球的 `collected` 信號。將信號連接到一個方法，該方法調用 `LevelManager.add_experience(amount)`。
    6.  **HUD (遊戲介面):**
        *   創建 `game_hud.tscn` (`CanvasLayer`)。
        *   添加 `Label` 用於顯示等級，`ProgressBar` 用於顯示經驗條。
        *   在 `game_hud.gd` 中，監聽 `LevelManager` 的 `level_up` 和 `experience_updated` 信號，並用接收到的數據更新 UI 元素。
        *   在 `main.tscn` 中實例化 `game_hud.tscn`。
    7.  **Level-Up UI (升級畫面):**
        *   創建 `level_up_screen.tscn` (`CanvasLayer`)，默認隱藏 (`visible = false`)。
        *   添加一個 `Panel` 和幾個 `Button` 作為選項佔位符。
        *   在 `level_up_screen.gd` 中，監聽 `LevelManager` 的 `level_up` 信號。當信號觸發時：
            *   調用 `get_tree().paused = true` 來暫停遊戲。
            *   設置 `visible = true` 來顯示升級畫面。
        *   為其中一個按鈕的 `pressed` 信號連接一個方法，該方法會 `get_tree().paused = false` 並隱藏畫面 `visible = false`。
        *   在 `main.tscn` 中實例化 `level_up_screen.tscn`。

    請為所有新創建的腳本提供完整代碼，並說明需要在 Godot 編輯器中進行的節點連接和設置。
    ```

*   **驗收標準:**
    - [x] 敵人死亡時會在原地掉落經驗球。
    - [x] 玩家走近經驗球時，經驗球會被自動收集。
    - [x] 遊戲 HUD 能夠正確顯示當前等級和經驗值的進度。
    - [x] 收集經驗球會增加 `LevelManager` 中的經驗值，並更新 HUD。
    - [x] 當經驗值達到升級門檻時，遊戲會暫停。
    - [x] 升級時會彈出一個包含選項按鈕的畫面。
    - [x] 點擊升級選項後，遊戲會恢復正常，升級畫面消失。

*   **注意事項:**
    *   `LevelManager` 作為單例（Autoload）是管理全局遊戲狀態（如經驗和等級）的理想選擇。
    *   遊戲暫停 (`get_tree().paused = true`) 是一個強大的功能，但需要確保所有節點的 `Process Mode` 都設置正確（`Inherit`），以確保它們能被正確暫停。UI 層通常需要將其進程模式設置為 `When Paused` 才能在遊戲暫停時響應輸入。
    *   此任務中的升級選項只是佔位符，真正的武器/技能選擇邏輯將在後續任務中實現。

---
### **TASK005: 遊戲循環狀態機**
*   **版本:** 1.0
*   **狀態:** 完成
*   **描述:** 建立一個全局的遊戲狀態管理器，處理主選單、遊戲進行中、遊戲結束等不同場景之間的切換，完成 MVP 的核心循環。

*   **子任務清單:**
    1.  `SUB_005.1`: 創建 `game_manager.gd` 單例，用於管理遊戲狀態和場景切換。
    2.  `SUB_005.2`: 創建 `main_menu.tscn`，包含開始遊戲和退出遊戲的按鈕。
    3.  `SUB_005.3`: 將當前包含遊戲邏輯的主場景重構為 `game_world.tscn`。
    4.  `SUB_005.4`: 為玩家 `player.tscn` 添加 `HealthComponent`，並連接其死亡信號。
    5.  `SUB_005.5`: 創建 `game_over_screen.tscn`，在玩家死亡時顯示。
    6.  `SUB_005.6`: 將所有部分連接起來，實現從主選單 -> 遊戲 -> 遊戲結束 -> 返回主選單的完整流程。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK005`。

    **目標:** 建立一個完整的遊戲狀態流程，管理從啟動遊戲到遊戲結束再回到主選單的整個循環。

    **先決條件:**
    *   一個可玩的遊戲世界，目前可能在 `main.tscn` 中 (來自 TASK001-004)。
    *   一個 `HealthComponent` 場景 (來自 TASK003)。

    **詳細要求:**
    1.  **GameManager (遊戲管理器):**
        *   創建 `scripts/managers/game_manager.gd` 並在 "自動加載" 中註冊為 `GameManager` 單例。
        *   腳本需要一個 `current_scene` 變數來追蹤當前場景。
        *   實現 `switch_scene(scene_path: String)` 方法。這個方法應該：
            *   卸載 `current_scene` (`current_scene.queue_free()`)。
            *   加載新的場景資源 (`load(scene_path)`)。
            *   實例化新場景。
            *   將新場景添加到場景樹的根節點 (`get_tree().root.add_child(new_scene)`)。
            *   更新 `current_scene` 變數。
    2.  **場景重構:**
        *   將您當前的主遊戲場景 (可能名為 `main.tscn`) 另存為 `scenes/worlds/game_world.tscn`。
        *   創建一個新的 `main.tscn`，它將作為遊戲啟動時的空場景，`GameManager` 將在此基礎上加載其他場景。
        *   在專案設置中，確保 "主場景" 指向這個新的、空的 `main.tscn`。
    3.  **MainMenu (主選單):**
        *   創建 `scenes/ui/main_menu.tscn` (`Control` 節點)。
        *   添加標題 `Label`、"開始遊戲" `Button` 和 "退出遊戲" `Button`。
        *   附加 `main_menu.gd` 腳本。
        *   將 "開始遊戲" 按鈕的 `pressed` 信號連接到一個方法，該方法調用 `GameManager.switch_scene("res://scenes/worlds/game_world.tscn")`。
        *   將 "退出遊戲" 按鈕的 `pressed` 信號連接到 `get_tree().quit()`。
        *   在 `GameManager` 的 `_ready` 函數中，初始加載主選單：`switch_scene("res://scenes/ui/main_menu.tscn")`。
    4.  **玩家死亡邏輯:**
        *   打開 `player.tscn`，為其添加 `HealthComponent`。
        *   在 `player.gd` 中，監聽其 `HealthComponent` 的 `died` 信號。
        *   當信號觸發時，調用 `GameManager` 的一個新方法，例如 `end_game()`。
    5.  **GameOver Screen (遊戲結束畫面):**
        *   創建 `scenes/ui/game_over_screen.tscn` (`CanvasLayer`)。
        *   添加 "遊戲結束" `Label` 和一個 "返回主選單" `Button`。
        *   將按鈕的 `pressed` 信號連接到 `GameManager.switch_scene("res://scenes/ui/main_menu.tscn")`。
    6.  **整合流程:**
        *   在 `GameManager` 中實現 `end_game()` 方法。此方法應在當前遊戲世界之上，實例化並顯示 `game_over_screen.tscn`。它不應立即切換場景，而是疊加 UI。

    請為所有新創建的腳本提供完整代碼，並詳細說明場景切換的邏輯和節點連接。
    ```

*   **驗收標準:**
    - [x] 遊戲啟動時會顯示主選單。
    - [x] 點擊 "開始遊戲" 按鈕會卸載主選單並加載 `game_world.tscn`，遊戲開始。
    - [x] 玩家在遊戲中可以被傷害殺死。
    - [x] 玩家死亡時，遊戲會彈出遊戲結束畫面。
    - [x] 點擊 "返回主選單" 按鈕會將玩家帶回到主選單場景。
    - [x] 點擊主選單的 "退出遊戲" 按鈕會關閉應用程式。
    - [x] 所有場景切換流暢，沒有殘留的舊場景節點。

*   **注意事項:**
    *   使用 `get_tree().root.add_child()` 和手動管理 `current_scene` 是實現簡單場景管理的一種方法。對於更複雜的專案，可以研究 `SceneTree.change_scene_to_file()`，但手動管理可以提供更多的過渡控制。
    *   `GameManager` 作為單例，是處理全局狀態轉換的理想場所。
    *   確保 `game_over_screen` 是一個 `CanvasLayer`，這樣它才能正確地疊加在遊戲畫面上，而不是被添加到遊戲世界本身的坐標系中。

---
## 版本 2.0: 內容擴展

目標：在 MVP 的基礎上，大幅擴充遊戲內容，引入更多樣的玩法和更強的重玩價值。

---

---
## 版本 2.0: 內容擴展

目標：在 MVP 的基礎上，大幅擴充遊戲內容，引入更多樣的玩法和更強的重玩價值。

---

### **TASK006: 數據結構重構 - 資源化**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 為遊戲建立數據驅動的基礎。此任務專注於創建武器和技能的數據「藍圖」(自定义 Resource)，將它們的屬性與程式碼分離，為後續的系統實現和內容擴展做準備。

*   **子任務清單:**
    1.  `SUB_006.1`: 創建一個基礎的 `upgrade_data.gd` 腳本，定義所有升級選項（武器和技能）的通用屬性。
    2.  `SUB_006.2`: 創建 `weapon_data.gd` 腳本，繼承自 `upgrade_data.gd`，並添加武器專用的屬性。
    3.  `SUB_006.3`: 創建 `ability_data.gd` 腳本，繼承自 `upgrade_data.gd`，並添加技能專用的屬性。
    4.  `SUB_006.4`: 為我們在 MVP 版本中已創建的「魔法飛彈」武器，創建一個對應的 `magic_missile.tres` 資源文件作為範例。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成新的 `TASK006`。

    **目標:** 建立數據驅動架構的基礎，定義武器和技能的數據結構。

    **詳細要求:**
    1.  **創建資料夾:** 在 `scripts/` 目錄下創建一個 `resources` 子目錄，用於存放所有自定義資源腳本。
    2.  **基礎升級數據 (UpgradeData):**
        *   在 `scripts/resources/` 中創建新腳本 `upgrade_data.gd`。
        *   使其繼承自 `Resource`。
        *   使用 `class_name UpgradeData` 將其註冊為全局類。
        *   導出以下變數：
            *   `id: String` (唯一標識符)
            *   `upgrade_name: String` (顯示名稱)
            *   `description: String` (詳細描述)
    3.  **武器數據 (WeaponData):**
        *   在 `scripts/resources/` 中創建新腳本 `weapon_data.gd`。
        *   使其繼承自 `UpgradeData` (`extends UpgradeData`)。
        *   使用 `class_name WeaponData` 註冊。
        *   導出武器特有變數：
            *   `weapon_scene: PackedScene` (武器的場景文件)
            *   `stats_per_level: Array[Dictionary]` (一個陣列，每個元素是一個字典，描述該等級的屬性，例如 `[{"damage": 5, "cooldown": 1.0}, {"damage": 7, "cooldown": 0.9}]`)
    4.  **技能數據 (AbilityData):**
        *   在 `scripts/resources/` 中創建新腳本 `ability_data.gd`。
        *   使其繼承自 `AbilityData` (`extends UpgradeData`)。
        *   使用 `class_name AbilityData` 註冊。
        *   導出技能特有變數：
            *   `stat_bonuses: Dictionary` (一個字典，描述此技能提供的屬性加成，例如 `{"move_speed_multiplier": 1.1, "damage_multiplier": 1.05}`)
    5.  **創建範例資源:**
        *   在 Godot 編輯器的「檔案系統」面板中，右鍵點擊 -> 新建資源... -> 選擇 `WeaponData`。
        *   將其儲存為 `resources/weapons/magic_missile.tres`。
        *   在 Inspector 中填寫其屬性：`id`="magic_missile", `upgrade_name`="魔法飛彈", `description`="發射一枚追蹤飛彈。", `weapon_scene`= (拖入現有的魔法飛彈場景), `stats_per_level`= (手動添加第一級的數據)。

    請為 `upgrade_data.gd`, `weapon_data.gd`, `ability_data.gd` 提供完整的腳本代碼。
    ```

*   **驗收標準:**
    - [ ] `upgrade_data.gd`, `weapon_data.gd`, `ability_data.gd` 三個腳本已創建且內容正確。
    - [ ] 在 Godot 編輯器中，可以成功創建 `WeaponData` 和 `AbilityData` 類型的新資源文件。
    - [ ] `resources/weapons/magic_missile.tres` 資源文件已創建，並已填寫了基礎的第一級數據。
    - [ ] 專案可以無錯誤地運行（儘管此任務的功能尚未被遊戲邏輯使用）。

*   **注意事項:**
    *   此任務的重點是建立數據「契約」，將遊戲設計（數值、名稱）與程式碼分離。
    *   `class_name` 是關鍵，它讓 Godot 可以在整個專案中識別這些自定義資源類型。
    *   `stats_per_level` 的結構設計對於後續升級至關重要，一個包含字典的陣列是靈活且可擴展的選擇。

---

### **TASK007: 升級系統實現**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 建立處理數據資源的「引擎」，包括創建 `WeaponManager` 來管理武器，並重構升級畫面以動態顯示和應用升級選項。

*   **子任務清單:**
    1.  `SUB_007.1`: 創建 `PlayerStats` 資源，用於集中管理玩家的所有可變屬性。
    2.  `SUB_007.2`: 創建 `WeaponManager` 節點和腳本，負責在玩家身上掛載、升級和管理武器實例。
    3.  `SUB_007.3`: 在 `LevelManager` 中實現一個升級選項池和一個隨機選取升級項的函數。
    4.  `SUB_007.4`: 重構 `LevelUpScreen`，使其能從 `LevelManager` 獲取真實的升級數據並顯示出來。
    5.  `SUB_007.5`: 實現應用升級的邏輯，根據玩家的選擇，調用 `WeaponManager` 或修改 `PlayerStats`。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK007`。

    **目標:** 激活我們在 TASK006 中創建的數據結構，建立一個能實際運作的、數據驅動的升級系統。

    **先決條件:**
    *   `WeaponData` 和 `AbilityData` 的自定義資源腳本已存在 (來自 TASK006)。
    *   一個可觸發升級事件的 `LevelManager` (來自 TASK004)。
    *   一個基礎的 `LevelUpScreen` (來自 TASK004)。

    **詳細要求:**
    1.  **PlayerStats 資源:**
        *   創建 `scripts/resources/player_stats.gd`，使其繼承自 `Resource` 並賦予 `class_name PlayerStats`。
        *   在其中導出玩家的核心屬性，例如 `max_health: float = 100.0`, `move_speed: float = 300.0`, `damage_multiplier: float = 1.0`, `cooldown_multiplier: float = 1.0`。
        *   在 `player.gd` 腳本中，導出一個 `stats: PlayerStats` 變數。在 Godot 編輯器中為玩家場景創建一個新的 `PlayerStats` 資源實例並賦值給它。
        *   重構 `player.gd`，使其移動速度等屬性讀取自 `stats.move_speed`。
    2.  **WeaponManager 節點:**
        *   在 `player.tscn` 中添加一個 `Node`，命名為 `WeaponManager`，並附加新腳本 `scripts/managers/weapon_manager.gd`。
        *   `weapon_manager.gd` 需要：
            *   一個 `owned_weapons` 字典，用於存儲 `weapon_data.id` 和對應的武器場景實例。
            *   `add_weapon(weapon_data: WeaponData)` 方法：根據 `weapon_data.weapon_scene` 實例化武器，將其添加為子節點，並存儲到 `owned_weapons` 中。同時根據 `weapon_data.stats_per_level[0]` 初始化武器屬性。
            *   `upgrade_weapon(weapon_data: WeaponData)` 方法：查找對應的武器實例，並根據其數據資源中的下一級屬性更新它。
    3.  **LevelManager 升級池:**
        *   打開 `level_manager.gd` 單例腳本。
        *   添加一個導出變數 `@export var upgrade_pool: Array[Resource]`。
        *   在 Godot 編輯器中，將 `magic_missile.tres` 以及後續創建的武器/技能資源文件拖入此陣列。
        *   創建 `get_upgrade_options(count: int) -> Array` 方法，該方法從 `upgrade_pool` 中隨機選取 `count` 個不重複的升級選項並返回。
    4.  **重構 LevelUpScreen:**
        *   打開 `level_up_screen.gd`。
        *   當接收到 `level_up` 信號時，調用 `LevelManager.get_upgrade_options(3)`。
        *   遍歷返回的升級選項陣列，為每個選項更新一個按鈕的 `text`（使用 `upgrade.upgrade_name`）和 `disabled` 狀態。
        *   使用 `button.set_meta("upgrade_data", upgrade)` 將完整的數據資源與按鈕關聯。
    5.  **應用升級邏輯:**
        *   在 `level_up_screen.gd` 中，當一個升級按鈕被點擊時，獲取其元數據 `var upgrade = button.get_meta("upgrade_data")`。
        *   發出一個包含 `upgrade` 的新信號，例如 `upgrade_selected(upgrade_data)`。
        *   在 `player.gd` 中監聽此信號。創建一個 `apply_upgrade(upgrade_data)` 方法。
        *   在 `apply_upgrade` 方法中，使用 `is` 關鍵字判斷 `upgrade_data` 的類型：
            *   如果 `upgrade_data is WeaponData`，則調用 `weapon_manager.add_weapon` 或 `upgrade_weapon`。
            *   如果 `upgrade_data is AbilityData`，則遍歷其 `stat_bonuses` 字典，並將加成應用於 `stats` 資源中的對應屬性。
        *   升級應用後，恢復遊戲 (`get_tree().paused = false`) 並隱藏升級畫面。

    請提供所有新腳本和重構腳本的完整代碼。
    ```

*   **驗收標準:**
    - [x] 玩家的屬性由一個獨立的 `PlayerStats` 資源文件控制。
    - [x] `WeaponManager` 節點已添加到玩家場景中。
    - [x] `LevelManager` 中有一個可配置的升級池。
    - [x] 升級時，畫面上會顯示從升級池中隨機選取的真實選項（目前應只有 "魔法飛彈"）。
    - [x] 選擇 "魔法飛彈" 升級選項後，玩家的武器會根據 `magic_missile.tres` 中定義的下一級屬性得到增強。
    - [x] 選擇一個（未來添加的）技能選項後，`PlayerStats` 資源中的對應值會被修改。

*   **注意事項:**
    *   此任務的核心是將「數據」和「邏輯」串聯起來。`LevelManager` 負責提供數據，`LevelUpScreen` 負責展示，而 `Player` 和 `WeaponManager` 負責執行。
    *   `PlayerStats` 資源化是關鍵一步，它讓外部系統（如技能）修改玩家狀態變得簡單和安全，而無需直接訪問玩家腳本的內部變數。
    *   `get_upgrade_options` 的邏輯可以先做簡單的隨機選取，後續可以優化，例如過濾掉已滿級的選項。

---

### **TASK008: 內容擴展 - 新武器與技能**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 利用前序任務建立的數據驅動架構，為遊戲填充新的武器和技能內容，豐富玩家的策略選擇。

*   **子任務清單:**
    1.  `SUB_008.1`: 創建「守護者之盾」武器：一個圍繞玩家旋轉並對接觸到的敵人造成傷害的武器。
    2.  `SUB_008.2`: 創建「穿透光束」武器：一種定時發射可穿透多個敵人的遠程光束的武器。
    3.  `SUB_008.3`: 創建「敏捷光環」技能：一個被動提升玩家移動速度的技能。
    4.  `SUB_008.4`: 創建「時間奇點」技能：一個被動減少所有武器冷卻時間的技能。
    5.  `SUB_008.5`: 將所有新創建的武器和技能資源添加到 `LevelManager` 的升級池中，使其可以在遊戲中隨機出現。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK008`。

    **目標:** 為遊戲添加兩種新武器和兩種新技能，以豐富內容並測試我們數據驅動系統的擴展性。

    **先決條件:**
    *   一個功能完備的、數據驅動的升級系統 (來自 TASK006 和 TASK007)。
    *   `LevelManager` 中有一個 `upgrade_pool` 陣列。

    **詳細要求:**
    1.  **武器1: 守護者之盾 (Guardian Shield):**
        *   **場景創建:** 在 `scenes/weapons/` 中創建 `guardian_shield.tscn`。根節點為 `Node2D`。在其下創建一個 `Area2D` 子節點，命名為 `Shield`，並為其添加 Sprite 和 CollisionShape。
        *   **腳本邏輯 (`guardian_shield.gd`):**
            *   在 `_process(delta)` 中，讓 `Shield` 節點圍繞其父節點（即武器的根節點）旋轉。
            *   `Shield` 的 `Area2D` 需要連接 `body_entered` 信號，對進入的敵人造成傷害。注意：為避免在單幀內對同一敵人造成多次傷害，需要實現一個短暫的傷害計時器或在造成傷害後暫時禁用碰撞體。
        *   **資源創建:** 在 `resources/weapons/` 中創建 `guardian_shield.tres` (`WeaponData`)。填寫名稱、描述，並鏈接到 `guardian_shield.tscn`。為其 `stats_per_level` 添加多級數據（例如，增加傷害、增加護盾數量、提升旋轉速度）。

    2.  **武器2: 穿透光束 (Piercing Beam):**
        *   **場景創建:**
            *   創建 `scenes/weapons/projectiles/piercing_beam_projectile.tscn`。根節點為 `Area2D`，使用一個細長的矩形作為其 Sprite 和 CollisionShape。
            *   創建 `scenes/weapons/piercing_beam_weapon.tscn`。這是一個 `Node2D`，包含一個 `Timer` 用於冷卻。
        *   **腳本邏輯:**
            *   `piercing_beam_projectile.gd`: 此投射物在擊中敵人後不應 `queue_free()`，以便穿透。可以設置一個 `life_timer` 或在移動到螢幕外後銷毀。
            *   `piercing_beam_weapon.gd`: 類似於魔法飛彈的武器腳本，但它實例化的是 `piercing_beam_projectile.tscn`。
        *   **資源創建:** 在 `resources/weapons/` 中創建 `piercing_beam.tres` (`WeaponData`)。填寫數據，鏈接到對應場景，並定義多級屬性（增加傷害、縮短冷卻、加長光束等）。

    3.  **技能1: 敏捷光環 (Agility Aura):**
        *   **資源創建:** 在 `resources/abilities/` 中創建 `agility_aura.tres` (`AbilityData`)。
        *   填寫其屬性：`id`="agility_aura", `upgrade_name`="敏捷光環", `description`="提升 10% 移動速度。"
        *   在 `stat_bonuses` 字典中添加鍵值對：`"move_speed_multiplier": 1.1`。

    4.  **技能2: 時間奇點 (Temporal Singularity):**
        *   **資源創建:** 在 `resources/abilities/` 中創建 `temporal_singularity.tres` (`AbilityData`)。
        *   填寫其屬性：`id`="temporal_singularity", `upgrade_name`="時間奇點", `description`="所有武器的冷卻時間減少 8%。"
        *   在 `stat_bonuses` 字典中添加鍵值對：`"cooldown_multiplier": 0.92`。

    5.  **更新升級池:**
        *   在 Godot 編輯器中，找到 `LevelManager` 單例的場景或腳本。
        *   將剛剛創建的四個 `.tres` 資源文件拖拽到其 `upgrade_pool` 導出變數的陣列中。

    請為所有新創建的場景和資源提供必要的腳本代碼和配置說明。
    ```

*   **驗收標準:**
    - [ ] 「守護者之盾」武器已實現，可以在遊戲中作為升級選項出現，並能正常運作。
    - [ ] 「穿透光束」武器已實現，可以在遊戲中作為升級選項出現，並能正常運作。
    - [ ] 「敏捷光環」技能已實現，選擇後能正確提升玩家的移動速度。
    - [ ] 「時間奇點」技能已實現，選擇後能正確減少所有武器的攻擊間隔。
    - [ ] 所有四個新的升級選項都已添加到 `LevelManager` 的升級池中，並能在升級時隨機出現。

*   **注意事項:**
    *   對於守護者之盾這類持續性傷害的武器，處理好單個敵人不會在短時間內被重複觸發傷害的邏輯非常重要，否則會導致傷害值異常。
    *   穿透光束的生命週期管理需要考慮周全，以避免場景中殘留過多已射出螢幕的投射物，影響性能。
    *   這一步是檢驗我們數據驅動架構是否成功的關鍵。添加新內容應該只需要創建新的場景和資源文件，而不需要修改核心的管理器代碼。

---

### **TASK009: 多樣化的敵人與AI**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 實現多種類型的敵人，每種都有獨特的行為模式和攻擊方式，增加遊戲的挑戰性和趣味性。

*   **子任務:**
    1.  `SUB_009.1`: 創建基礎敵人類型系統，支援不同敵人的屬性配置
    2.  `SUB_009.2`: 實現追蹤型敵人，會主動追蹤玩家
    3.  `SUB_009.3`: 實現射擊型敵人，會從遠處發射投射物
    4.  `SUB_009.4`: 實現衝鋒型敵人，會快速衝向玩家
    5.  `SUB_009.5`: 為每種敵人類型創建獨特的視覺效果和音效

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK009`。

    **目標:** 創建多樣化的敵人系統，每種敵人都有獨特的行為模式和攻擊方式。

    **先決條件:**
    *   基礎的敵人系統 (TASK003)
    *   玩家移動和攻擊系統 (TASK002)

    **詳細要求:**
    1.  **敵人類型系統:**
        *   創建一個 `EnemyType` 枚舉或常量系統，定義不同的敵人類型
        *   為每種敵人類型創建對應的資源文件，包含屬性配置
        *   修改 `Enemy` 腳本，使其能夠根據類型表現不同的行為

    2.  **追蹤型敵人:**
        *   實現簡單的追蹤AI，敵人會朝著玩家的方向移動
        *   添加適當的移動速度和轉向邏輯
        *   考慮添加視野範圍限制

    3.  **射擊型敵人:**
        *   實現遠程攻擊邏輯，敵人會保持距離並發射投射物
        *   創建敵人的投射物系統
        *   添加攻擊冷卻時間和射程限制

    4.  **衝鋒型敵人:**
        *   實現衝鋒攻擊模式，敵人會快速衝向玩家
        *   添加衝鋒後的短暫眩暈或冷卻時間
        *   實現衝鋒的視覺效果

    5.  **視覺和音效:**
        *   為每種敵人類型創建獨特的精靈圖或模型
        *   添加適當的動畫效果
        *   實現對應的音效和粒子效果

    請提供所有新腳本和資源文件的完整代碼，並解釋如何配置和測試新的敵人系統。
    ```

*   **驗收標準:**
    - [ ] 遊戲中至少有3種不同類型的敵人
    - [ ] 每種敵人都有獨特的行為模式和攻擊方式
    - [ ] 敵人的AI行為流暢且具有挑戰性
    - [ ] 每種敵人都有對應的視覺效果和音效

---

### **TASK010: 頭目戰**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 實現具有挑戰性的頭目戰，頭目擁有獨特的攻擊模式和階段轉換機制。

*   **子任務:**
    1.  `SUB_010.1`: 設計頭目的基礎架構和生命值系統
    2.  `SUB_010.2`: 實現頭目的多階段攻擊模式
    3.  `SUB_010.3`: 創建頭目戰的特殊機制和環境效果
    4.  `SUB_010.4`: 實現頭目戰的獎勵系統

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK010`。

    **目標:** 創建具有挑戰性和趣味性的頭目戰系統。

    **先決條件:**
    *   多樣化的敵人系統 (TASK009)
    *   玩家升級系統 (TASK006)

    **詳細要求:**
    1.  **頭目基礎架構:**
        *   創建 `Boss` 腳本，繼承自 `Enemy` 或創建新的基類
        *   實現頭目的生命值系統，支援多階段轉換
        *   添加頭目特有的UI元素（如生命值條）

    2.  **多階段攻擊模式:**
        *   設計至少3個不同的攻擊階段
        *   每個階段都有獨特的攻擊模式和難度
        *   實現階段轉換的觸發條件和視覺效果

    3.  **特殊機制:**
        *   創建頭目戰特有的環境效果
        *   實現需要玩家策略應對的攻擊模式
        *   添加頭目戰的背景音樂和音效

    4.  **獎勵系統:**
        *   設計頭目戰的獎勵內容
        *   實現獎勵的發放邏輯
        *   添加成就或統計記錄

    請提供頭目系統的完整實現，包括腳本、場景和資源文件。
    ```

*   **驗收標準:**
    - [x] 頭目戰具有挑戰性和策略性
    - [x] 頭目擁有多階段攻擊模式
    - [x] 頭目戰有獨特的視覺和音效效果
    - [x] 擊敗頭目後有適當的獎勵

---

### **TASK011: 局外成長系統**
*   **版本:** 2.0
*   **狀態:** 完成
*   **描述:** 實現遊戲外的永久成長系統，讓玩家在多次遊戲中能夠持續變強。

*   **子任務:**
    1.  `SUB_011.1`: 創建永久統計數據系統
    2.  `SUB_011.2`: 實現技能樹或天賦系統
    3.  `SUB_011.3`: 創建成就和挑戰系統
    4.  `SUB_011.4`: 實現數據持久化存儲

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK011`。

    **目標:** 創建遊戲外的永久成長系統，增加遊戲的重玩價值。

    **先決條件:**
    *   完整的遊戲循環系統
    *   玩家統計數據系統

    **詳細要求:**
    1.  **永久統計數據:**
        *   創建 `PermanentStats` 資源，存儲玩家的永久統計數據
        *   實現統計數據的保存和加載
        *   設計統計數據對遊戲的影響機制

    2.  **技能樹系統:**
        *   創建可視化的技能樹界面
        *   實現技能點數的獲取和消耗
        *   設計技能的效果和升級路徑

    3.  **成就系統:**
        *   創建成就的定義和觸發條件
        *   實現成就的追蹤和顯示
        *   添加成就獎勵機制

    4.  **數據持久化:**
        *   使用 Godot 的 `ConfigFile` 或自定義存儲系統
        *   實現數據的加密和備份
        *   添加數據遷移和版本控制

    請提供完整的局外成長系統實現，包括UI、數據管理和持久化存儲。
    ```

*   **驗收標準:**
    - [x] 玩家的進步在遊戲間得到保留
    - [x] 技能樹系統具有策略性和可玩性
    - [x] 成就系統能夠激勵玩家重複遊戲
    - [x] 數據存儲穩定且安全

---

## 版本 3.0: 優化與潤飾

### **TASK012: 性能優化**
*   **版本:** 3.0
*   **狀態:** 完成
*   **描述:** 對遊戲進行全面的性能分析和優化，確保在同螢幕存在大量敵人和投射物的情況下，遊戲依然能流暢運行。

*   **子任務清單:**
    1.  `SUB_012.1`: 設置性能監控面板，識別瓶頸（CPU、GPU）。
    2.  `SUB_012.2`: 實現敵人節點複用池 (Node Pooling)，減少大量生成/銷毀節點帶來的效能開銷。
    3.  `SUB_012.3`: 優化繪圖調用 (Draw Calls)，例如將靜態背景元素批次處理。
    4.  `SUB_012.4`: 針對高頻執行的程式碼（如敵人 AI、投射物移動）進行演算法層面的優化。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 性能優化的開發者，請為 Wavebreaker 專案完成 `TASK012`。

    **目標:** 顯著提升遊戲在中低端硬體上的運行幀率，特別是在遊戲後期。

    **詳細要求:**
    1.  **性能監控:**
        *   指導如何在遊戲中啟用 Godot 的性能監控器（Debugger -> Monitors），並解釋如何解讀關鍵指標，如 `physics_process` 時間、`process` 時間和 `draw_calls`。
    2.  **節點複用池 (Node Pooling):**
        *   創建一個 `NodePoolManager` 單例腳本。
        *   該腳本需要一個 `get_node(scene: PackedScene)` 方法，此方法會先嘗試從池中獲取一個已禁用的節點，如果池為空，則實例化一個新節點。
        *   還需要一個 `return_node(node: Node)` 方法，此方法會禁用傳入的節點（隱藏並停止其處理），並將其存儲在池中以備後用。
        *   重構 `EnemySpawner` 和所有武器的 `fire()` 方法，使其從 `NodePoolManager` 獲取和返還敵人/投射物實例，而不是直接調用 `instance()` 和 `queue_free()`。
    3.  **繪圖優化:**
        *   分析遊戲世界場景，提出可以合併為單個 `Sprite2D` 或 `TileMap` 的靜態背景層建議，以減少 `draw_calls`。
    4.  **程式碼優化:**
        *   審查 `enemy.gd` 的 `_physics_process`，分析是否有可以優化的部分，例如，可以降低遠處敵人的 AI 更新頻率。

    請為 `NodePoolManager.gd` 提供完整腳本，並提供重構 `EnemySpawner` 和武器腳本的具體程式碼範例。
    ```

*   **驗收標準:**
    - [x] 遊戲中內置了可開關的性能監控顯示。
    - [x] 敵人、投射物等頻繁生成的物件已全部通過節點複用池進行管理。
    - [x] 在後期敵人密集的波次中，遊戲幀率相比優化前有顯著提升。
    - [x] `draw_calls` 數量維持在一個合理的範圍內。

*   **注意事項:**
    *   節點複用是此類遊戲性能優化的關鍵，必須優先實現。
    *   過早的微觀程式碼優化是無益的，應先通過分析器找到真正的性能瓶頸，再進行針對性優化。
    *   優化時需不斷測試，確保沒有因節點複用（如狀態未重置）而引入新的 Bug。

---

### **TASK013: 音效與音樂**
*   **版本:** 3.0
*   **狀態:** 完成
*   **描述:** 建立一個集中的音訊管理器，並為遊戲添加音效和背景音樂，極大地提升遊戲的沉浸感和打擊感。

*   **子任務清單:**
    1.  `SUB_013.1`: 創建 `AudioManager` 單例，用於播放音效和音樂。
    2.  `SUB_013.2`: 在設置選單中添加音效（SFX）和音樂（BGM）的音量控制滑桿。
    3.  `SUB_013.3`: 尋找並導入一批 placeholder 音效（武器開火、擊中、敵人死亡、UI點擊）。
    4.  `SUB_013.4`: 尋找並導入至少兩首 placeholder 背景音樂（選單、戰鬥）。
    5.  `SUB_013.5`: 在遊戲邏輯的關鍵節點（如開火、受傷）調用 `AudioManager` 播放對應音效。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的遊戲開發者，請為 Wavebreaker 專案完成 `TASK013`。

    **目標:** 建立一個完整的音訊系統，並集成基礎的音效和音樂。

    **詳細要求:**
    1.  **AudioManager 單例:**
        *   創建 `scripts/managers/audio_manager.gd` 並註冊為 `AudioManager` 自動加載單例。
        *   在 `AudioManager` 中，創建兩個 `AudioStreamPlayer` 節點作為子節點，一個命名為 `sfx_player`，一個為 `bgm_player`。
        *   創建 `play_sfx(sfx_resource: AudioStream, volume_db: float = 0.0)` 方法，該方法設置 `sfx_player.stream` 並調用 `play()`。
        *   創建 `play_bgm(bgm_resource: AudioStream)` 方法，該方法設置 `bgm_player.stream` 並調用 `play()`。確保音樂是循環的 (`bgm_player.finished` 信號重新連接到 `play()`)。
        *   創建 `set_sfx_volume(volume_db: float)` 和 `set_bgm_volume(volume_db: float)` 方法，用於控制對應音訊總線的音量。
    2.  **音量控制UI:**
        *   在遊戲的設置選單中，添加兩個 `HSlider`，分別用於控制 SFX 和 BGM 音量。
        *   將滑桿的 `value_changed` 信號連接到對應的 `AudioManager.set_sfx_volume` 和 `set_bgm_volume` 方法。
    3.  **資源導入:**
        *   指導如何在 Godot 中導入音訊文件，並將它們保存為 `AudioStream` 資源。
    4.  **邏輯整合:**
        *   在武器的 `fire()` 方法中，調用 `AudioManager.play_sfx()`。
        *   在投射物的 `_on_body_entered()` 方法中，如果擊中敵人，調用 `AudioManager.play_sfx()`。
        *   在 `GameManager` 的場景切換邏輯中，進入主選單時調用 `AudioManager.play_bgm(menu_music)`，進入遊戲世界時調用 `AudioManager.play_bgm(battle_music)`。

    請提供 `AudioManager.gd` 的完整腳本，並給出整合音訊播放的程式碼範例。
    ```

*   **驗收標準:**
    - [ ] 遊戲中有背景音樂，且選單和戰鬥場景的音樂不同。
    - [ ] 攻擊、擊中、點擊按鈕等關鍵操作都有音效反饋。
    - [ ] 可以在設置中獨立調節音效和音樂的音量。
    - [ ] 音訊播放穩定，沒有延遲或中斷。

*   **注意事項:**
    *   使用音訊總線（Audio Buses）來管理不同類型的音訊是 Godot 的最佳實踐。`AudioManager` 應控制總線的音量，而不是單個 `AudioStreamPlayer` 的音量。
    *   注意 placeholder 資源的版權問題，確保它們是開源或已獲授權的。

---

### **TASK014: 視覺效果與粒子系統**
*   **版本:** 3.0
*   **狀態:** 計畫中
*   **描述:** 通過為關鍵遊戲事件（如攻擊命中、死亡、升級）添加粒子效果，來增強視覺反饋和遊戲的「爽快感」。

*   **子任務清單:**
    1.  `SUB_014.1`: 創建一個通用的 `VFXManager`，用於在指定位置生成和管理視覺特效。
    2.  `SUB_014.2`: 設計並創建一個用於敵人被擊中時的 `GPUParticles2D` 特效場景。
    3.  `SUB_014.3`: 設計並創建一個用於敵人死亡時的爆炸 `GPUParticles2D` 特效場景。
    4.  `SUB_014.4`: 設計並創建一個玩家升級時的環繞光環 `GPUParticles2D` 特效場景。
    5.  `SUB_014.5`: 將特效的播放在遊戲邏輯的正確時機（擊中、死亡、升級）進行調用。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 視覺特效的開發者，請為 Wavebreaker 專案完成 `TASK014`。

    **目標:** 建立一個視覺特效系統，並為核心玩法添加粒子效果。

    **詳細要求:**
    1.  **VFXManager:**
        *   創建 `scripts/managers/vfx_manager.gd` 並註冊為 `VFXManager` 單例。
        *   實現 `play_vfx(vfx_scene: PackedScene, position: Vector2)` 方法。此方法應從節點池（可複用 TASK012 的 `NodePoolManager`）獲取一個特效實例，設置其 `global_position`，然後將其添加到場景樹中。特效播放完畢後應自動返回池中（可使用 `finished` 信號）。
    2.  **創建粒子特效:**
        *   指導如何在 Godot 中創建一個基礎的 `GPUParticles2D` 節點。
        *   **擊中特效:** 創建 `hit_effect.tscn`。配置其 `Process Material`，使其產生一個短暫、快速發散的火花效果。設置 `one_shot` 為 true。
        *   **死亡特效:** 創建 `death_effect.tscn`。配置其產生一個更大、更持久的爆炸效果，可能包含一些煙霧和碎片。設置 `one_shot` 為 true。
        *   **升級特效:** 創建 `levelup_effect.tscn`。配置其產生一個從玩家中心向上升騰的光環或粒子流。
    3.  **邏輯整合:**
        *   在投射物擊中敵人時，調用 `VFXManager.play_vfx(hit_effect, enemy.global_position)`。
        *   在敵人 `_on_health_component_died()` 方法中，調用 `VFXManager.play_vfx(death_effect, self.global_position)`。
        *   在 `LevelManager` 發出 `level_up` 信號時，讓玩家節點監聽此信號，並調用 `VFXManager.play_vfx(levelup_effect, player.global_position)`。

    請提供 `VFXManager.gd` 的完整腳本，並詳細說明創建粒子特效的步驟和關鍵參數。
    ```

*   **驗收標準:**
    - [ ] 敵人被擊中時，會在擊中點產生火花特效。
    - [ ] 敵人死亡時，會產生爆炸特效。
    - [ ] 玩家升級時，會產生慶祝性的視覺特效。
    - [ ] 特效系統通過節點池管理，對性能影響較小。

*   **注意事項:**
    *   `GPUParticles2D` 性能遠高於 `CPUParticles2D`，在粒子數量較多時應優先使用。
    *   好的視覺特效應簡潔、有力且不遮擋遊戲關鍵資訊。
    *   確保特效節點在播放完畢後能被正確地從場景樹移除或返還到池中，避免內存洩漏。

---

### **TASK015: 本地化與多語言支援**
*   **版本:** 3.0
*   **狀態:** 計畫中
*   **描述:** 實現多語言支援，將所有面向玩家的文字進行本地化，使遊戲能夠觸及更廣泛的國際玩家群體。

*   **子任務清單:**
    1.  `SUB_015.1`: 設置 Godot 的本地化系統，創建翻譯用的 CSV 文件。
    2.  `SUB_015.2`: 遍歷所有 UI 場景，將 `Label` 和 `Button` 的 `text` 屬性替換為翻譯鍵。
    3.  `SUB_015.3`: 將所有 `UpgradeData` 資源中的 `upgrade_name` 和 `description` 屬性也改為使用翻譯鍵。
    4.  `SUB_015.4`: 在設置選單中添加一個語言切換的選項。

*   **AI 程式設計提示詞:**
    ```
    作為一名專精於 Godot Engine 的開發者，請為 Wavebreaker 專案完成 `TASK015`。

    **目標:** 建立一個完整的本地化系統，支援中英文切換。

    **詳細要求:**
    1.  **設置本地化:**
        *   指導如何在「專案設置」->「本地化」分頁中添加支持的語言（如 `en`, `zh`）。
        *   指導如何創建一個 `localization.csv` 文件，並在「本地化」分頁中將其設置為主翻譯文件。
        *   CSV 文件應包含 `keys,en,zh` 的標頭。
    2.  **UI 文本替換:**
        *   提供一個範例，展示如何將一個 `Label` 的 `text` 屬性從 "Start Game" 改為 `tr("UI_START_GAME")`。
        *   為 `UI_START_GAME` 在 `localization.csv` 中添加對應條目：`UI_START_GAME,"Start Game","開始遊戲"`。
        *   要求遍歷所有 UI 相關場景，對所有可見文字執行此操作。
    3.  **資源文本替換:**
        *   修改 `UpgradeData` 腳本，將 `@export var upgrade_name: String` 改為 `@export var name_key: String`。
        *   修改 `LevelUpScreen` 中顯示名稱的程式碼，從 `upgrade.upgrade_name` 改為 `tr(upgrade.name_key)`。
        *   為 `magic_missile.tres` 資源更新此屬性，並在 CSV 中添加對應條目。
    4.  **語言切換器:**
        *   在設置選單中，添加一個 `OptionButton`。
        *   在 `_ready` 中，為其添加選項 "English" 和 "中文"。
        *   將其 `item_selected` 信號連接到一個方法，該方法根據選擇的索引調用 `TranslationServer.set_locale("en")` 或 `TranslationServer.set_locale("zh")`。

    請提供修改後 `UpgradeData.gd` 和 `LevelUpScreen.gd` 的程式碼片段，並給出一個完整的 `localization.csv` 範例。
    ```

*   **驗收標準:**
    - [ ] 遊戲中的所有 UI 文本（按鈕、標籤等）都已使用翻譯鍵。
    - [ ] 所有武器和技能的名稱、描述都已使用翻譯鍵。
    - [ ] 可以在設置選單中即時切換中英文，且所有文本都隨之正確更新。
    - [ ] 添加新語言只需要修改 CSV 文件和添加字體，無需改動程式碼。

*   **注意事項:**
    *   Godot 的 `tr()` 函數是實現本地化的核心。
    *   對於中文、日文等非拉丁語系，需要在專案中導入支持這些語言的字體文件（`.ttf` 或 `.otf`），並將 UI 節點的字體設置為該字體，否則文字將無法顯示。
    - `TranslationServer.set_locale()` 會在調用時自動重載所有UI，非常方便。

---

## 版本 4.0: 發布與維護

### **TASK016: 遊戲測試與平衡**
*   **版本:** 4.0
*   **狀態:** 計畫中
*   **描述:** 進行密集的內部和（可能的）小範圍外部測試，收集反饋，並根據數據和玩家體驗調整遊戲的核心數值，以達到最佳的遊戲平衡和趣味性。

*   **子任務:**
    1.  `SUB_016.1`: 制定一份詳細的測試用例清單 (Test Case Checklist)。
    2.  `SUB_016.2`: 至少進行 10 次完整的遊戲流程測試，記錄武器/技能的選擇頻率和通關成功率。
    3.  `SUB_016.3`: 根據測試數據，調整武器/技能的強度、敵人的生命值/攻擊力、升級曲線等核心數值。
    4.  `SUB_016.4`: 修復在測試過程中發現的所有 A/B 級 Bug。

*   **AI 輔助提示詞:**
    ```
    作為一名資深的遊戲平衡設計師和 QA 測試員，請為 Wavebreaker 專案完成 `TASK016`。

    **目標:** 系統性地測試遊戲，並基於數據和體驗調整遊戲平衡。

    **詳細要求:**
    1.  **測試用例生成:**
        *   請為遊戲的核心功能（玩家移動、武器系統、升級、敵人AI、UI流程）生成一份結構化的測試用例清單，包含步驟、預期結果和實際結果欄位。
    2.  **數據分析與平衡建議:**
        *   假設我提供了以下的測試數據：「魔法飛彈被選取率 80%，光束被選取率 15%，盾牌被選取率 5%。玩家平均生存時間為 15 分鐘，但很少能見到 Boss。」
        *   請基於以上數據，分析當前可能存在的平衡性問題。
        *   提出具體的調整建議，例如：「建議略微降低魔法飛彈的基礎傷害或成長性，同時提升光束的穿透能力或盾牌的傷害範圍，以鼓勵玩家嘗試不同的 Build。同時，考慮略微降低 10-15 分鐘波次的敵人強度，讓更多玩家有機會體驗到 Boss 戰。」
    3.  **Bug 分級:**
        *   請定義 A、B、C 三級 Bug 的標準。例如：A級為導致遊戲崩潰或進程阻塞的 Bug；B級為嚴重影響體驗但可繞過的 Bug；C級為不影響流程的視覺或文字錯誤。

    請提供結構化的測試用例範本和詳細的平衡分析報告。
    ```

*   **驗收標準:**
    - [ ] 遊戲的核心玩法循環沒有 A/B 級 Bug。
    - [ ] 不同的武器和技能之間的強度差距在一個合理的範圍內，沒有「唯一最優解」。
    - [ ] 遊戲的難度曲線平滑，能為新手提供學習空間，也為老手提供挑戰。

*   **注意事項:**
    *   遊戲平衡是一個持續迭代的過程，沒有絕對的「完美」。此階段的目標是達到一個良好、可玩的基線。
    *   邀請真實的外部玩家進行測試能獲得最寶貴的反饋。

---

### **TASK017: 平台發布準備**
*   **版本:** 4.0
*   **狀態:** 計畫中
*   **描述:** 根據目標發布平台（如 Steam, Itch.io）的要求，完成所有必要的配置、構建和打包工作。

*   **子任務:**
    1.  `SUB_017.1`: 創建遊戲的圖標、橫幅、截圖等商店頁面美術資源。
    2.  `SUB_017.2`: 在 Godot 的導出預設中，為 Windows, macOS, Linux 創建導出範本。
    3.  `SUB_017.3`: 配置特定平台的整合功能（如 Steam SDK 的成就系統）。
    4.  `SUB_017.4`: 編寫一份清晰的「如何運行遊戲」的說明文檔。

*   **AI 輔助提示詞:**
    ```
    作為一名有經驗的獨立遊戲發行專家，請為 Wavebreaker 專案完成 `TASK017`。

    **目標:** 準備好在 Steam 和 Itch.io 上發布遊戲所需的一切。

    **詳細要求:**
    1.  **商店資源清單:**
        *   請列出在 Steam 和 Itch.io 商店頁面上架一個遊戲，通常需要哪些尺寸和格式的圖片資源（例如：Capsule Image, Header, Screenshots）。
    2.  **Godot 導出指南:**
        *   請提供在 Godot 中為 Windows, macOS, Linux 設置導出範本的詳細步驟，包括如何處理 macOS 的代碼簽名和公證問題。
    3.  **Steamworks 整合:**
        *   假設要集成 Steam 成就，請簡要說明需要使用哪個 Godot 第三方插件（如 GodotSteam），以及在程式碼中觸發一個成就的大致流程（例如 `Steam.set_achievement("ACH_WIN_GAME")`）。

    請提供一份包含所有資源規格和步驟的清單。
    ```

*   **驗收標準:**
    - [ ] 遊戲可以被成功地導出為 Windows (.exe), macOS (.dmg/.app), 和 Linux 的可執行文件。
    - [ ] 所有商店頁面所需的宣傳材料都已準備就緒。
    - [ ] 遊戲在目標平台上可以正常啟動和運行。

*   **注意事項:**
    *   為不同平台進行構建和打包是發行前最繁瑣的步驟之一，需要預留充足的時間。
    - 特別是 macOS 的發布，流程比 Windows/Linux 複雜得多，需要有開發者帳號並熟悉其生態。

---

### **TASK018: 社區支援與更新**
*   **版本:** 4.0
*   **狀態:** 計畫中
*   **描述:** 遊戲發布後的初步社區維護計畫，包括建立反饋渠道和準備第一個小型更新。

*   **子任務:**
    1.  `SUB_018.1`: 創建一個公開的 Bug 反饋表單（如 Google Forms）。
    2.  `SUB_018.2`: 在遊戲主選單添加指向社區（如 Discord, 討論區）和反饋表單的連結。
    3.  `SUB_018.3`: 根據發布初期的反饋，規劃一個包含 Bug 修復和少量內容（如一個新武器或技能）的 `v1.0.1` 更新。

*   **AI 輔助提示詞:**
    ```
    作為一名社區經理，請為 Wavebreaker 專案完成 `TASK018`。

    **目標:** 制定一個基礎的、可執行的發布後社區維護計畫。

    **詳細要求:**
    1.  **反饋渠道:**
        *   請設計一個 Google Forms 表單，用於收集玩家的 Bug 報告。應包含哪些欄位（例如：遊戲版本、問題描述、重現步驟、電腦配置）？
    2.  **社區連結:**
        *   請提供在 Godot UI 中創建一個可以打開網頁連結的按鈕的程式碼範例 (`OS.shell_open("https://...")`)。
    3.  **更新計畫:**
        *   請基於「維持玩家活躍度」和「快速響應問題」的原則，為第一個發布後補丁（v1.0.1）的內容提出建議。

    請提供表單設計建議和程式碼範例。
    ```

*   **驗收標準:**
    - [ ] 遊戲內有清晰的指引，告知玩家如何提交反饋。
    - [ ] 第一個發布後更新的內容已初步規劃完畢。

*   **注意事項:**
    *   發布只是起點，積極與玩家社區互動並快速響應他們的問題，是遊戲能否長期成功的關鍵。

