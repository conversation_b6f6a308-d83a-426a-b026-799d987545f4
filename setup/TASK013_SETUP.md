# TASK013 設置與使用指南（Godot 4）

本文件協助你在 Godot 編輯器內完成音訊系統的設置。請依序操作，僅需幾分鐘。

## 1) 建立 Audio Bus 佈局
為了能獨立控制 SFX 與 BGM 音量，請建立兩條匯流排：
- Master（預設存在）
- SFX（子匯流排，Parent 設為 Master）
- BGM（子匯流排，Parent 設為 Master）

操作步驟：
1. 開啟 Godot → Audio → Audio Bus Layout（或在 Editor 上方選單：Project → Project Settings → Audio → Bus Layout）。
2. 在 Master 之下新增兩條 Bus，名稱分別為 SFX 與 BGM。
3. 可選：為 SFX 加上 Limiter/Compressor，避免疊加過載破音。
4. 儲存佈局（預設為 res://default_bus_layout.tres）。

## 2) 註冊 AudioManager 為 Autoload 單例
1. Project → Project Settings → Autoload。
2. 路徑填入 `res://scripts/managers/audio_manager.gd`，名稱 `AudioManager`。
3. 按「Add」，關閉對話框。

（我們已在 project.godot 代為加入，若編輯器未自動同步，請手動確認一次並保存）

## 3) 指定 BGM/SFX 資源
AudioManager 暴露了幾個 @export 欄位，方便你在編輯器綁定資源：
- menu_bgm：主選單音樂
- battle_bgm：戰鬥音樂
- ui_click_sfx：UI 點擊音效
- weapon_fire_sfx：武器開火音效（通用）
- hit_sfx：命中音效（通用）
- enemy_death_sfx：敵人死亡音效（可通用）

建議目錄結構（可自建）：
- res://audio/sfx/
- res://audio/bgm/

匯入音訊方式：
1. 將 .wav/.ogg 檔拖入 FileSystem 面板對應資料夾。
2. 選取資源，在 Inspector 確認 Import 設定（預設即可），按 Reimport。
3. 回到場景或單例，於 Inspector 將欄位拖拽綁定對應的 AudioStream（例如 OggVorbis/AudioStreamWAV）。

## 4) 連動遊戲邏輯
我們已集成以下行為：
- 進入主選單：GameManager 會呼叫 `AudioManager.play_bgm(menu_bgm)`。
- 開始遊戲/進入戰鬥：GameManager 會呼叫 `AudioManager.play_bgm(battle_bgm)`。
- UI 按鈕：主選單按鈕點擊時會呼叫 `AudioManager.play_ui_click()`。
- 武器開火（MagicMissile）：在發射投射物時會呼叫 `AudioManager.play_sfx(weapon_fire_sfx)`。
- 我方投射物命中：在 `scripts/weapons/projectile.gd` 命中後呼叫 `AudioManager.play_sfx(hit_sfx)`。
- 敵人攻擊/死亡：`scripts/enemies/enemy.gd` 會改用 `AudioManager.play_sfx(...)` 播放其 `EnemyData` 定義的音效。

## 5) 驗收與測試
1. 從主選單進入與返回主選單時，應切換不同的 BGM。
2. 點擊任一主選單按鈕，能聽到 UI 點擊音效。
3. 戰鬥中：
   - 武器射擊時有開火音效。
   - 投射物命中時有命中音效。
   - 敵人攻擊/死亡有對應音效（若資源未綁定則無聲）。
4. 在 Audio→Audio Bus Layout 中拉動 SFX/BGM 的音量，應能即時影響對應聲音。

## 6) 常見問題
- 聽不到任何聲音：
  - 確認音量未被設為過低（Audio Bus 或作業系統音量）。
  - 確認 AudioManager 的 export 欄位已綁定有效的音訊資源。
  - Console 若出現「找不到音訊匯流排 'SFX'/'BGM'」警告，請依步驟 1 建立對應 Bus。
- BGM 不循環：
  - AudioManager 會在 finished 信號回撥中重新播放，請確認沒有手動 stop()。
- 仍看到舊的臨時 AudioStreamPlayer：
  - 我們保留了後備方案函式以兼容舊邏輯，不影響使用。建議逐步遷移到 AudioManager。

完成以上步驟後，即完成 TASK013 的編輯器設置。
