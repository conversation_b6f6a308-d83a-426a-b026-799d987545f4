# TASK010: 頭目戰 - 設置指南

本文件說明如何將 `TASK010` 中創建的頭目戰系統正確整合到 Wavebreaker 專案中。

## 1. 確認新文件

請確保以下文件已成功創建在您的專案中：

*   `scripts/resources/enemy_data.gd` (已修改，增加了 `BOSS` 類型)
*   `scripts/components/boss_health_component.gd`
*   `scripts/enemies/boss.gd`
*   `resources/enemies/boss_data.tres`
*   `scripts/ui/boss_health_bar.gd`
*   `scripts/managers/difficulty_manager.gd` (已修改，增加了頭目生成邏輯)
*   `scripts/worlds/game_world.gd` (已修改，連接了頭目UI)

## 2. 設置 `boss.tscn` 場景

您需要手動創建或驗證 `scenes/enemies/boss.tscn`。請按照以下結構配置：

*   **Boss (CharacterBody2D)**
    *   **Script:** `res://scripts/enemies/boss.gd`
    *   **Groups:** `["enemies", "boss"]` (請確保添加了 `boss` 組)
    *   **Inspector -> Boss (Script Variables):**
        *   `Projectile Scene`: 拖拽 `res://scenes/weapons/enemy_projectile.tscn` 到此欄位。
        *   `Experience Orb Scene Override`: 拖拽 `res://scenes/pickups/experience_orb.tscn` 到此欄位。
    *   **Sprite2D (Sprite2D)**
        *   **Texture:** `res://icon.svg` (或任何您想用的臨時頭目圖像)
        *   **Transform -> Scale:** `(4, 4)`
    *   **CollisionShape2D (CollisionShape2D)**
        *   **Shape:** 新建 `CircleShape2D`，設置其 `radius` 為 `64`。
    *   **BossHealthComponent (Node)**
        *   **Script:** `res://scripts/components/boss_health_component.gd`
        *   **Inspector -> Health Stages (Array):** 設置陣列大小為 `3`，並分別填入每階段的生命值，例如 `[1000, 1500, 2000]`。
    *   **AttackTimer (Timer)**
        *   **Wait Time:** `2.0`
        *   **One Shot:** `true` (勾選)
    *   **StunTimer (Timer)**
        *   **Wait Time:** `3.0`
        *   **One Shot:** `true` (勾選)

## 3. 設置 `boss_health_bar.tscn` 場景

您需要手動創建或驗證 `scenes/ui/boss_health_bar.tscn`。

*   **BossHealthBar (HBoxContainer)**
    *   **Script:** `res://scripts/ui/boss_health_bar.gd`
    *   **Inspector -> Health Bar Scene:** 拖拽 `res://scenes/components/health_bar.tscn` 到此欄位。
    *   **Layout:**
        *   在 **Layout** 面板中，選擇 **Anchors Preset** 為 **Top Wide**。
        *   手動調整 **Margins**: `Left` 設為 `200`, `Top` 設為 `20`, `Right` 設為 `-200`, `Bottom` 設為 `40` (或其他您認為合適的高度)。

## 4. 更新 `game_world.tscn`

打開 `scenes/worlds/game_world.tscn` 場景。

1.  **添加頭目血條UI:**
    *   在 `GameHUD` 節點下，實例化 `scenes/ui/boss_health_bar.tscn`。
2.  **配置 DifficultyManager:**
    *   選中場景中的 `DifficultyManager` 節點。
    *   在 Inspector 中，找到 `Boss Scene` 屬性，並將 `res://scenes/enemies/boss.tscn` 拖拽到該欄位。

## 5. 測試

完成以上所有設置後，運行 `main.tscn` 進入遊戲。等待5分鐘（或您在 `DifficultyManager` 中設置的時間），頭目應該會自動生成，並且螢幕頂部會顯示其多階段的生命值條。嘗試與其戰鬥，觀察其攻擊模式和階段轉換，並在擊敗它後檢查是否掉落了大量經驗球。
