# TASK012 設置與使用指南（Godot 4）

本文件協助你在 Godot 編輯器內完成性能監控與節點池的設置，並驗收本次優化。

## 1) 啟用內建性能監視器（Monitors）
- 遊戲執行中：打開上方功能表 Debugger → Monitors。
- 重要指標解讀：
  - physics_process: 物理步進耗時（每幀 ms）— 高表示物理解算/AI 過重。
  - process: 一般邏輯耗時— 腳本/非物理運算過重。
  - draw_calls: 畫面呼叫次數— 較高通常來自大量 Sprite/材質切換。
- 額外：啟用我們內建的疊加層 PerfMonitor（F3 切換顯示）。

## 2) 設置 Autoload 單例
1. 在 Project → Project Settings → Autoload：
   - 添加 `res://scripts/managers/node_pool_manager.gd`，名稱 `NodePoolManager`。
   - 添加 `res://scripts/managers/perf_monitor.gd`，名稱 `PerfMonitor`。
2. 點擊「Add」後按「Close」，重新運行專案。

（我們已將這兩個自動加載寫入 project.godot，若你在 Godot 看到重複項或失效，請以編輯器設定為準）

## 3) 物件池使用說明（已替換的重點）
- 敵人生成：EnemySpawner 由 `instantiate()` 改為 `NodePoolManager.get_node(scene)`。
- 投射物：MagicMissileWeapon、ShooterEnemy、PiercingBeamWeapon 的發射統一改為透過池取得。
- 回收：
  - 友方投射物 `scripts/weapons/projectile.gd` 觸發命中/飛行超距時呼叫 `_return_to_pool()`。
  - 敵人投射物 `scripts/weapons/enemy_projectile.gd` 命中/壽命結束時回收。
  - 敵人 `scripts/enemies/enemy.gd` 在死亡流程尾端改為延遲呼叫 `_return_to_pool()`。
- 節點若實作 `on_taken_from_pool()`/`on_returned_to_pool()` 可在取用/歸還時重置狀態。

## 4) 驗收建議流程
1. 在 `scenes/worlds/game_world.tscn` 執行遊戲。
2. 按 F3 開關性能疊加，觀察 FPS、process/physics、draw_calls。
3. 提高敵人數量（在 EnemySpawner Inspector 調整 max_enemies / spawn_interval）。
4. 關注 GC/Alloc 掉幀是否下降（利用 Monitors 的 Memory / Objects）。

## 5) 注意與已知限制
- 回收 vs queue_free：若你新增腳本仍使用 `queue_free()`，請改為 `_return_to_pool()` 或 NodePoolManager.return_node(node)。
- 狀態重置：務必在 `on_taken_from_pool()` 內重置移動、碰撞/監聽、計時器，避免「殘留狀態」Bug。
- Scene 路徑：池以 `PackedScene.resource_path` 作為 key，非資源化生成的動態節點會落在 `__dynamic__` 分組，建議盡量用資源檔。

## 6) 後續可優化方向（選做）
- 遠處敵人降頻：在敵人/AI 中以距離閾值降低 `_physics_process` 更新頻率。
- 繪圖批次：盡量把靜態背景合併為單一 TileMap 或大圖，減少 draw_calls。
- VFX 也可接入 NodePoolManager（見 TASK014）。

完成以上步驟後，即可開始壓測並逐步優化瓶頸。祝順利！

