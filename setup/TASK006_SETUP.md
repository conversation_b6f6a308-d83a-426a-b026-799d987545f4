# TASK006 設置指南 - 數據結構重構

## 概述
此任務已完成基礎腳本的創建，現在需要在 Godot 編輯器中手動創建資源文件來完成設置。

## 已完成的腳本和場景
- ✅ `scripts/resources/upgrade_data.gd` - 基礎升級數據類別
- ✅ `scripts/resources/weapon_data.gd` - 武器數據類別
- ✅ `scripts/resources/ability_data.gd` - 技能數據類別
- ✅ `scripts/resources/test_resources.gd` - 測試腳本（可選）
- ✅ `scenes/weapons/magic_missile_weapon.tscn` - 魔法飛彈武器場景
- ✅ `scenes/weapons/projectile.tscn` - 投射物場景
- ✅ `scenes/components/health_component.tscn` - 健康組件場景

## 需要在 Godot 編輯器中完成的步驟

### 步驟 1: 創建魔法飛彈武器資源
1. 在 Godot 編輯器中，打開「檔案系統」面板
2. 右鍵點擊 `resources/weapons/` 資料夾
3. 選擇「新建資源...」
4. 在搜尋欄中輸入「WeaponData」
5. 選擇 `WeaponData` 並點擊「創建」
6. 將資源儲存為 `magic_missile.tres`

### 步驟 2: 配置魔法飛彈資源屬性
在 Inspector 面板中填寫以下屬性：

**基礎屬性：**
- `id`: `"magic_missile"`
- `upgrade_name`: `"魔法飛彈"`
- `description`: `"發射一枚追蹤飛彈，對敵人造成傷害"`

**武器特有屬性：**
- `weapon_scene`: 拖入 `scenes/weapons/magic_missile_weapon.tscn` 場景（已創建）
- `stats_per_level`: 點擊陣列並添加第一個元素

**第一級統計數據（陣列的第一個元素）：**
```gdscript
{
    "damage": 5,
    "cooldown": 1.0,
    "projectile_speed": 500
}
```

### 步驟 3: 驗證設置
1. 確保所有腳本都沒有編譯錯誤
2. 檢查 `magic_missile.tres` 資源文件是否正確創建
3. 運行專案，確保沒有錯誤

### 步驟 4: 測試資源類別（可選）
1. 在 `main.tscn` 中添加一個 `Node` 節點
2. 將 `scripts/resources/test_resources.gd` 腳本附加到該節點
3. 運行遊戲，查看控制台輸出，確認所有測試都通過
4. 測試完成後，可以刪除測試節點和測試腳本

## 注意事項
- 此任務的重點是建立數據「契約」，將遊戲設計與程式碼分離
- `class_name` 讓 Godot 可以在整個專案中識別這些自定義資源類型
- `stats_per_level` 的結構設計對於後續升級系統至關重要

## 下一步
完成此任務後，將可以進行 TASK007，實現升級系統的具體邏輯。
