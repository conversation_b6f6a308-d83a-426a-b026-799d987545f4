# TASK009: 多樣化的敵人與AI 設置指南

## 概述
本任務實現了多樣化的敵人系統，包括三種不同類型的敵人，每種都有獨特的行為模式和攻擊方式。

## 新增功能

### 1. 敵人類型系統
- **追蹤型敵人 (Tracker)**: 會主動追蹤玩家，進行近戰攻擊
- **射擊型敵人 (Shooter)**: 保持距離，發射投射物攻擊
- **衝鋒型敵人 (Charger)**: 快速衝向玩家，造成高傷害

### 2. AI狀態機系統
- 閒置 (IDLE)
- 巡邏 (PATROL)
- 追蹤 (CHASE)
- 攻擊 (ATTACK)
- 撤退 (RETREAT)
- 眩暈 (STUNNED)

### 3. 敵人投射物系統
- 支援穿透和彈跳
- 可配置的傷害和速度
- 視覺效果和音效支援

## 設置步驟

### 步驟 1: 更新敵人場景
1. 打開 `scenes/enemies/enemy.tscn`
2. 在根節點 `Enemy` 的腳本屬性中，設置 `enemy_data` 為對應的資源文件：
   - 追蹤型: `res://resources/enemies/tracker_enemy.tres`
   - 射擊型: `res://resources/enemies/shooter_enemy.tres`
   - 衝鋒型: `res://resources/enemies/charger_enemy.tres`

### 步驟 2: 配置敵人生成器
1. 打開 `scenes/worlds/game_world.tscn`
2. 找到 `EnemySpawner` 節點
3. 在腳本屬性中設置：
   - `spawn_radius`: 100.0
   - `max_enemies`: 10
   - `spawn_interval`: 3.0

### 步驟 3: 測試不同敵人類型
1. 運行遊戲
2. 觀察敵人生成器會隨機生成三種不同類型的敵人
3. 每種敵人都有不同的行為模式：
   - 追蹤型會直接追蹤玩家
   - 射擊型會保持距離並發射投射物
   - 衝鋒型會衝向玩家並造成眩暈

## 腳本文件說明

### 核心腳本
- `enemy_data.gd`: 敵人類型枚舉和屬性配置
- `enemy_ai_state_machine.gd`: AI狀態機系統
- `enemy.gd`: 基礎敵人類，支援多種類型
- `shooter_enemy.gd`: 射擊型敵人實現
- `charger_enemy.gd`: 衝鋒型敵人實現
- `enemy_projectile.gd`: 敵人投射物系統

### 資源文件
- `tracker_enemy.tres`: 追蹤型敵人配置
- `shooter_enemy.tres`: 射擊型敵人配置
- `charger_enemy.tres`: 衝鋒型敵人配置

## 自定義配置

### 調整敵人屬性
在對應的 `.tres` 資源文件中修改：
- `max_health`: 最大生命值
- `move_speed`: 移動速度
- `damage`: 攻擊傷害
- `detection_range`: 偵測範圍
- `attack_range`: 攻擊範圍

### 調整生成機率
在 `enemy_spawner.gd` 的 `_select_enemy_type()` 方法中修改機率分配。

### 添加新的敵人類型
1. 在 `EnemyData.EnemyType` 枚舉中添加新類型
2. 創建對應的資源文件
3. 實現特定的敵人腳本
4. 在生成器中添加生成邏輯

## 故障排除

### 常見問題
1. **敵人無法移動**: 檢查 `enemy_data` 是否正確設置
2. **AI狀態機不工作**: 確認 `EnemyAIStateMachine` 腳本已正確附加
3. **投射物不發射**: 檢查 `projectile_scene` 是否設置

### 調試技巧
- 在控制台查看敵人生成日誌
- 使用 Godot 的調試器檢查敵人狀態
- 在腳本中添加 `print()` 語句追蹤AI狀態變化

## 性能優化建議

1. **對象池**: 對於投射物和效果，考慮使用對象池
2. **LOD系統**: 遠距離敵人可以使用簡化的AI邏輯
3. **空間分割**: 使用四叉樹或網格系統優化碰撞檢測

## 下一步
完成 TASK009 後，可以繼續進行：
- TASK010: 頭目戰系統
- 敵人視覺效果和音效優化
- 敵人AI行為的進一步改進
