# 任務11設定：Meta 升級畫面 UI

本指南將說明如何建立 Meta 升級畫面 UI 並將其連接到主選單。

## 第一部分：建立 Meta 升級畫面

1.  **建立新場景：**
    *   前往 `場景` -> `新場景`。
    *   選擇 `使用者介面` 作為根節點。這將會建立一個 `Control` 節點。
    *   將根節點的類型變更為 `CanvasLayer`。將其重新命名為 `MetaUpgradeScreen`。
    *   將場景另存為 `res://scenes/ui/meta_upgrade_screen.tscn`。

2.  **附加指令碼：**
    *   選取 `MetaUpgradeScreen` 根節點。
    *   在偵測器中，找到 `Script` 屬性並附加 `res://scripts/ui/meta_upgrade_screen.gd`。

3.  **建構 UI 版面配置：**
    *   新增一個 `Panel` 作為 `MetaUpgradeScreen` 的子節點，用作背景。使用 2D 視圖頂部的「版面配置」選單將其錨定到中心。
    *   新增一個 `VBoxContainer` 作為 `Panel` 的子節點。
    *   在 `VBoxContainer` 內，新增以下節點：
        *   一個名為 `TitleLabel` 的 `Label`，文字為「永久升級」。
        *   一個名為 `CurrencyLabel` 的 `Label`。選取它，前往偵測器，在「進階」部分下，勾選「在擁有者中唯一名稱」方塊。這允許指令碼使用 `%CurrencyLabel` 找到它。
        *   一個名為 `HealthLabel` 的 `Label`。同樣啟用「在擁有者中唯一名稱」。
        *   一個名為 `SpeedLabel` 的 `Label`。同樣啟用「在擁有者中唯一名稱」。
        *   一個名為 `UpgradeHealthButton` 的 `Button`，文字為「升級生命值 (費用: 100)」。
        *   一個名為 `UpgradeSpeedButton` 的 `Button`，文字為「升級速度 (費用: 150)」。
        *   一個名為 `BackButton` 的 `Button`，文字為「返回選單」。

4.  **連接信號：**
    *   選取 `UpgradeHealthButton`。前往 `節點` 標籤頁 -> `信號`。雙擊 `pressed()` 並將其連接到 `MetaUpgradeScreen` 中的 `_on_upgrade_health_button_pressed` 方法。
    *   選取 `UpgradeSpeedButton`。將其 `pressed()` 信號連接到 `_on_upgrade_speed_button_pressed`。
    *   選取 `BackButton`。將其 `pressed()` 信號連接到 `_on_back_button_pressed`。

## 第二部分：在主選單中新增按鈕

1.  **開啟主選單場景：**
    *   開啟 `res://scenes/ui/main_menu.tscn`。

2.  **新增升級按鈕：**
    *   在存放「開始遊戲」和「離開」按鈕的 `VBoxContainer` 中，新增一個新的 `Button`。
    *   將其重新命名為 `UpgradesButton`，並將其文字設定為「升級」。將其放置在「開始」和「離開」按鈕之間。

3.  **連接其信號：**
    *   選取新的 `UpgradesButton`。
    *   將其 `pressed()` 信號連接到 `main_menu.gd` 指令碼中的一個新方法。

4.  **更新 `main_menu.gd`：**
    *   `main_menu.gd` 的指令碼已經更新了所需的功能 (`_on_upgrades_button_pressed`)。您只需要將您建立的按鈕的信號連接到這個現有的函式即可。

現在，當您執行遊戲時，您可以從主選單導覽至升級畫面，購買升級（如果您有貨幣），並且資料將會被儲存。