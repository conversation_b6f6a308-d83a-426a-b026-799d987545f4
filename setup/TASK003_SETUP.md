# Godot 編輯器設置指南

本指南將引導你在 Godot 編輯器中完成所有必要的設置，以確保 Wavebreaker 專案正常運行。

## 🎯 必須完成的設置步驟

### 1. 輸入映射設置 (Input Map)

按照 `INPUT_MAPPING_SETUP.md` 的說明設置玩家控制：

1. **專案** → **專案設置** (Project → Project Settings)
2. 左側選單選擇 **輸入映射** (Input Map)
3. 添加以下四個動作：

| 動作名稱 | 鍵盤按鍵 | 額外按鍵 |
|---------|---------|---------|
| `move_left` | A | 左方向鍵 |
| `move_right` | D | 右方向鍵 |
| `move_up` | W | 上方向鍵 |
| `move_down` | S | 下方向鍵 |

### 2. 碰撞層設置 (Collision Layers)

**重要：** 碰撞層設置對於投射物正確命中敵人至關重要。

1. **專案** → **專案設置** (Project → Project Settings)
2. 左側選單選擇 **圖層名稱** → **2D 物理** (Layer Names → 2D Physics)
3. 設置以下層名稱：

| 層級 | 名稱 | 用途 |
|-----|------|------|
| 1 | Player | 玩家角色 |
| 2 | Enemy | 敵人角色 |
| 3 | Projectile | 投射物 |

### 3. 場景檢查清單

確保以下場景已正確設置：

#### 3.1 Player 場景 (`scenes/player/player.tscn`)
- [x] 根節點：`CharacterBody2D` (名稱: "Player")
- [x] 群組：`player_group`
- [x] 碰撞層：1 (Player)
- [x] 碰撞遮罩：2 (Enemy)
- [x] 腳本：`scripts/player/player.gd`
- [x] 子節點：`HealthComponent` (實例，`max_health = 50.0`)
- [x] 子節點：`HealthBar` (實例)
- [x] 子節點：`WeaponMount/MagicMissileWeapon`
- [x] 信號連接：`HealthComponent.died` → `Player._on_health_component_died()`

#### 3.2 Enemy 場景 (`scenes/enemies/enemy.tscn`)
- [x] 根節點：`CharacterBody2D` (名稱: "Enemy")
- [x] 群組：`enemy_group`
- [x] 碰撞層：2 (Enemy)
- [x] 碰撞遮罩：1 (Player)
- [x] 腳本：`scripts/enemies/enemy.gd`
- [x] 子節點：`HealthComponent` (實例)
- [x] 子節點：`HealthBar` (實例)
- [x] 信號連接：`HealthComponent.died` → `Enemy._on_health_component_died()`

#### 3.3 Projectile 場景 (`scenes/weapons/projectile.tscn`)
- [x] 根節點：`Area2D` (名稱: "Projectile")
- [x] 碰撞層：3 (Projectile)
- [x] 碰撞遮罩：2 (Enemy)
- [x] 腳本：`scripts/weapons/projectile.gd`

#### 3.4 Main 場景 (`main.tscn`)
- [x] 根節點：`Node2D` (名稱: "Main")
- [x] 子節點：`Player` (實例自 `scenes/player/player.tscn`)
- [x] 子節點：`EnemySpawner` (腳本: `scripts/enemy_spawner.gd`)
- [x] 子節點：`WaveTimer` (Timer 節點，`wait_time=1.0`, `autostart=true`)
- [x] 信號連接：`WaveTimer.timeout` → `EnemySpawner._on_wave_timer_timeout()`

### 4. 檢查 Inspector 設置

#### 4.1 EnemySpawner 設置
1. 選擇 Main 場景中的 `EnemySpawner` 節點
2. 在 Inspector 中設置：
   - `Enemy Scene`: 拖入 `scenes/enemies/enemy.tscn`
   - `Spawn Distance`: 800.0

#### 4.2 MagicMissileWeapon 設置
1. 選擇 Player 場景中的 `WeaponMount/MagicMissileWeapon` 節點
2. 在 Inspector 中設置：
   - `Projectile Scene`: 拖入 `scenes/weapons/projectile.tscn`
   - `Fire Rate`: 0.5
   - `Detection Range`: 500.0

### 5. 驗證設置

完成所有設置後，按 **F5** 運行專案：

#### 預期行為：
1. ✅ 玩家可以使用 WASD 或方向鍵移動
2. ✅ 敵人每秒生成一個，會追蹤玩家
3. ✅ 玩家每 0.5 秒自動發射藍色投射物
4. ✅ 投射物會朝向最近的敵人發射
5. ✅ 投射物擊中敵人時，敵人頭頂會顯示紅色血條
6. ✅ 敵人受到足夠傷害後會消失 (預設 10 HP，投射物 5 傷害)
7. ✅ 滿血敵人不顯示血條
8. ✅ **新增**: 玩家有 50 HP，頭頂會顯示血條 (受傷時)
9. ✅ **新增**: 敵人接觸玩家時會造成 10 點傷害 (每秒最多一次)
10. ✅ **新增**: 玩家死亡時會在控制台印出 "Game Over!" 訊息

#### 如果出現問題：

**投射物不朝向敵人：**
- 檢查敵人是否在 `enemy_group` 群組中
- 檢查 `MagicMissileWeapon` 的 `Detection Range` 設置

**投射物不傷害敵人：**
- 檢查碰撞層和遮罩設置
- 確保投射物碰撞遮罩包含敵人層 (2)
- 確保敵人碰撞層設置為 2
- 確保投射物碰撞層設置為 3

**血條不顯示：**
- 檢查 `HealthBar` 是否正確連接到 `HealthComponent`
- 攻擊敵人後血條才會顯示 (滿血時隱藏)

**敵人不追蹤玩家：**
- 檢查玩家是否在 `player_group` 群組中

**敵人不傷害玩家：**
- 檢查敵人和玩家的碰撞層和遮罩設置
- 確保敵人碰撞遮罩包含玩家層 (1)
- 確保玩家碰撞層設置為 1

**玩家血條不顯示：**
- 讓敵人接觸玩家造成傷害後血條才會顯示
- 檢查 `HealthComponent` 是否正確連接到 `HealthBar`

## 🔧 除錯提示

1. 使用 **遠程** → **遠程檢查器** 來檢查運行時的節點狀態
2. 在 `enemy.gd` 中添加 `print()` 語句來檢查變數值
3. 確保所有 `.tscn` 文件都已保存
4. 重新開啟專案如果遇到奇怪問題

## 📁 檔案結構檢查

確保你的專案結構如下：

```
wavebreaker/
├── scenes/
│   ├── components/
│   │   ├── health_component.tscn
│   │   └── health_bar.tscn
│   ├── enemies/
│   │   └── enemy.tscn
│   ├── player/
│   │   └── player.tscn
│   └── weapons/
│       └── projectile.tscn
├── scripts/
│   ├── components/
│   │   ├── health_component.gd
│   │   └── health_bar.gd
│   ├── enemies/
│   │   └── enemy.gd
│   ├── player/
│   │   └── player.gd
│   ├── weapons/
│   │   ├── magic_missile_weapon.gd
│   │   └── projectile.gd
│   └── enemy_spawner.gd
├── main.tscn
└── project.godot
```

完成所有設置後，你應該擁有一個完整的可玩遊戲！🎉
