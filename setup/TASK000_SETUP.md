# 輸入映射設置指南

為了讓玩家能夠控制角色移動，你需要在 Godot 編輯器中設置輸入映射。

## 設置步驟

1. 在 Godot 編輯器中打開 **專案** → **專案設置** (Project → Project Settings)

2. 在左側選單中選擇 **輸入映射** (Input Map)

3. 在頂部的 "Action:" 輸入框中添加以下四個動作名稱，每次添加一個後點擊 "Add" 按鈕：

### 需要添加的動作：

#### move_left
- 按下 "Add" 後，點擊動作右側的 "+" 按鈕
- 選擇 "Key" 並按下 **A** 鍵
- 再次點擊 "+" 按鈕，選擇 "Key" 並按下 **左方向鍵**

#### move_right  
- 按下 "Add" 後，點擊動作右側的 "+" 按鈕
- 選擇 "Key" 並按下 **D** 鍵
- 再次點擊 "+" 按鈕，選擇 "Key" 並按下 **右方向鍵**

#### move_up
- 按下 "Add" 後，點擊動作右側的 "+" 按鈕
- 選擇 "Key" 並按下 **W** 鍵
- 再次點擊 "+" 按鈕，選擇 "Key" 並按下 **上方向鍵**

#### move_down
- 按下 "Add" 後，點擊動作右側的 "+" 按鈕
- 選擇 "Key" 並按下 **S** 鍵
- 再次點擊 "+" 按鈕，選擇 "Key" 並按下 **下方向鍵**

## 完成設置

設置完成後，點擊 "關閉" (Close) 保存設置。

現在你可以：
- 使用 **W/A/S/D** 鍵控制玩家移動
- 使用 **方向鍵** 控制玩家移動
- 在 Inspector（屬性檢視器）面板中調整玩家的 `move_speed` 參數來改變移動速度

## 測試

運行 `main.tscn` 場景來測試玩家控制是否正常工作。
