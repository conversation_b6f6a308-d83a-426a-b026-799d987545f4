# TASK004 設定指南

本文檔包含完成 TASK004：經驗與升級UI 所需的手動設定步驟。

## 1. 註冊 LevelManager 為 Autoload/單例

1. 開啟 Godot 編輯器
2. 前往 **專案** → **專案設定**
3. 點擊 **Autoload** 標籤
4. 點擊 **新增** 按鈕
5. 設定以下數值：
   - **路徑**：`res://scripts/managers/level_manager.gd`
   - **節點名稱**：`LevelManager`
6. 點擊 **新增** 來註冊單例

## 2. 驗證場景引用

以下場景應該會自動引用，但請驗證它們是否存在：

- `res://scenes/pickups/experience_orb.tscn`
- `res://scenes/ui/game_hud.tscn`
- `res://scenes/ui/level_up_screen.tscn`

## 3. 測試實作

1. 執行 `main.tscn` 場景
2. 移動並讓敵人生成
3. 擊殺敵人來收集經驗球
4. 觀察HUD更新經驗和等級資訊
5. 當你升級時，遊戲應該會暫停並顯示升級畫面
6. 點擊任何選項或繼續來恢復遊戲

## 4. 故障排除

### 經驗球沒有生成
- 檢查敵場景中的 `experience_orb_scene` 是否正確設定
- 驗證敵人的 `_spawn_experience_orb()` 方法是否被調用

### 經驗沒有被收集
- 確保玩家有 `PickupArea` 節點
- 檢查 `PickupArea` 是否有正確的碰撞遮罩（應該是 1）
- 驗證經驗球是否在 "experience_orbs" 群組中

### HUD 沒有更新
- 確認 LevelManager 已正確註冊為 Autoload
- 檢查 HUD 腳本是否正確連接到 LevelManager 信號
- 驗證 UI 元素是否在 HUD 腳本中正確引用

### 升級畫面沒有出現
- 確保升級畫面設定為 `process_mode = 2`（暫停時處理）
- 檢查畫面是否正確連接到 LevelManager 信號
- 驗證畫面預設設定為 `visible = false`

## 5. 檔案結構

實作後，你的專案應該有這些新檔案：

```
scenes/
├── pickups/
│   └── experience_orb.tscn
└── ui/
    ├── game_hud.tscn
    └── level_up_screen.tscn

scripts/
├── managers/
│   └── level_manager.gd
├── pickups/
│   └── experience_orb.gd
└── ui/
    ├── game_hud.gd
    └── level_up_screen.gd
```

## 6. 後續步驟

一旦 TASK004 完成，你可以繼續進行：
- TASK005：遊戲循環狀態機
- 在升級畫面中實作實際的升級效果
- 新增更複雜的敵人AI
- 實作額外的武器和能力
