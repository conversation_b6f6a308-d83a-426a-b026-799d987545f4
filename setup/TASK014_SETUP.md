# TASK014 視覺效果與粒子系統 - Godot 編輯器設置指南

本文檔說明如何在 Godot 編輯器中設置 TASK014 創建的視覺特效系統。

## ⚠️ 重要提醒

如果你看到場景文件解析錯誤，請先在 Godot 編輯器中打開專案一次，讓 Godot 重新導入所有資源。這是正常的，因為場景文件需要在編輯器中被正確解析和導入。

## 1. 驗證 VFXManager 自動加載

1. 打開 **專案 → 專案設置**
2. 在左側選擇 **自動加載** 分頁
3. 確認 `VFXManager` 已經在列表中，路徑為 `*res://scripts/managers/vfx_manager.gd`
4. 如果沒有，請點擊 **添加** 按鈕，設置：
   - **路徑**: `res://scripts/managers/vfx_manager.gd`
   - **節點名稱**: `VFXManager`
   - **啟用**: 勾選

## 2. 配置擊中特效場景 (hit_effect.tscn)

1. 打開 `scenes/effects/hit_effect.tscn`
2. 選擇 `HitParticles` 節點
3. 在 **Inspector** 中配置以下屬性：

### 基本設置
- **Emitting**: false（預設不發射）
- **Amount**: 20（粒子數量）
- **Lifetime**: 0.8（生命週期）
- **One Shot**: true（單次發射）
- **Explosiveness**: 1.0（爆炸性）

### Process Material 設置
1. 點擊 **Process Material** 旁的下拉箭頭
2. 選擇 **新建 ParticleProcessMaterial**
3. 配置以下屬性：
   - **Direction**: (0, -1, 0)
   - **Initial Velocity Min**: 50.0
   - **Initial Velocity Max**: 150.0
   - **Angular Velocity Min**: -180.0
   - **Angular Velocity Max**: 180.0
   - **Gravity**: (0, 98, 0)
   - **Scale Min**: 0.5
   - **Scale Max**: 1.5
   - **Color**: 橙黃色 (1, 0.8, 0.2, 1)

### 紋理設置
- **Texture**: 拖入 `icon.svg` 或其他小型紋理

## 3. 配置死亡特效場景 (death_effect.tscn)

1. 打開 `scenes/effects/death_effect.tscn`
2. 配置 `ExplosionParticles` 節點：

### 基本設置
- **Amount**: 50
- **Lifetime**: 1.5
- **One Shot**: true
- **Explosiveness**: 1.0

### Process Material 設置
- **Direction**: (0, -1, 0)
- **Spread**: 45.0
- **Initial Velocity Min**: 80.0
- **Initial Velocity Max**: 200.0
- **Angular Velocity Min**: -360.0
- **Angular Velocity Max**: 360.0
- **Gravity**: (0, 150, 0)
- **Scale Min**: 0.8
- **Scale Max**: 2.0
- **Color**: 橙紅色 (1, 0.4, 0.1, 1)

3. 配置 `SmokeParticles` 節點：
- **Amount**: 30
- **Lifetime**: 2.0
- **One Shot**: true
- **Explosiveness**: 0.8
- 其他設置類似 ExplosionParticles，但顏色較暗

## 4. 配置升級特效場景 (levelup_effect.tscn)

1. 打開 `scenes/effects/levelup_effect.tscn`
2. 配置 `AuraParticles` 節點：

### 基本設置
- **Amount**: 80
- **Lifetime**: 3.0
- **One Shot**: true
- **Explosiveness**: 0.3

### Process Material 設置
- **Direction**: (0, -1, 0)
- **Spread**: 30.0
- **Initial Velocity Min**: 100.0
- **Initial Velocity Max**: 250.0
- **Angular Velocity Min**: -90.0
- **Angular Velocity Max**: 90.0
- **Gravity**: (0, -50, 0)（向上的重力）
- **Scale Min**: 1.0
- **Scale Max**: 2.5
- **Color**: 藍色 (0.2, 0.8, 1, 1)

3. 配置 `SparkleParticles` 節點：
- **Amount**: 40
- **Lifetime**: 2.5
- **One Shot**: true
- **Explosiveness**: 0.5
- 其他設置類似 AuraParticles

## 5. 測試特效系統

### 方法一：使用測試場景
1. 在 Godot 編輯器中打開 `scenes/test_vfx_scene.tscn`
2. 運行這個場景（F6）
3. 查看控制台輸出，確認所有測試都通過
4. 使用以下按鍵測試特效：
   - **空白鍵**: 重新運行自動測試
   - **1**: 在滑鼠位置播放擊中特效
   - **2**: 在滑鼠位置播放死亡特效
   - **3**: 在滑鼠位置播放升級特效

### 方法二：在遊戲中測試
1. 運行主遊戲場景
2. 測試以下功能：
   - **擊中特效**: 攻擊敵人時應該看到橙色火花
   - **死亡特效**: 敵人死亡時應該看到爆炸和煙霧效果
   - **升級特效**: 玩家升級時應該看到藍色光環效果

## 6. 性能優化建議

### GPU Particles vs CPU Particles
- 本系統使用 `GPUParticles2D`，性能更好
- 如果在低端設備上遇到問題，可以改用 `CPUParticles2D`

### 粒子數量調整
- 如果性能不佳，可以減少 `Amount` 值
- 建議範圍：
  - 擊中特效：10-30 個粒子
  - 死亡特效：30-80 個粒子
  - 升級特效：50-120 個粒子

### 節點池優化
- 特效系統已整合節點池，自動回收特效實例
- 無需手動管理特效的生命週期

## 7. 自定義特效

### 添加新特效
1. 創建新的特效場景（繼承 Node2D）
2. 添加對應的腳本（實現 `play()` 方法和 `finished` 信號）
3. 在 VFXManager 中添加新的播放方法
4. 在需要的地方調用 `VFXManager.play_vfx()`

### 修改現有特效
- 調整 Process Material 的參數來改變特效外觀
- 修改 `effect_duration` 來改變特效持續時間
- 更換 `texture` 來使用不同的粒子紋理

## 8. 故障排除

### 場景文件解析錯誤
如果看到類似以下錯誤：
```
Parse Error: Parse error. [Resource file res://scenes/effects/hit_effect.tscn:27]
Failed loading resource: res://scenes/effects/hit_effect.tscn
```

**解決方法**：
1. 在 Godot 編輯器中打開專案
2. 讓編輯器完成資源導入過程
3. 如果仍有問題，嘗試重新打開特效場景文件並保存

### VFXManager 自動加載失敗
如果看到：
```
Failed to create an autoload, script 'res://scripts/managers/vfx_manager.gd' is not compiling
```

**解決方法**：
1. 確保所有特效場景文件都能正確加載
2. 在編輯器中打開 `vfx_manager.gd` 並檢查是否有語法錯誤
3. 重新啟動 Godot 編輯器

### 特效不顯示
1. 檢查 VFXManager 是否正確設為自動加載
2. 確認特效場景路徑正確
3. 檢查控制台是否有錯誤訊息
4. 確認特效場景中的粒子系統配置正確

### 性能問題
1. 減少粒子數量
2. 縮短特效持續時間
3. 考慮使用 CPUParticles2D

### 特效位置不正確
1. 確認調用 `play_vfx()` 時傳入的位置正確
2. 檢查特效場景的根節點是否為 Node2D

## 完成確認

完成設置後，你應該能看到：
- ✅ 投射物擊中敵人時的火花特效
- ✅ 敵人死亡時的爆炸特效
- ✅ 玩家升級時的光環特效
- ✅ 敵人攻擊玩家時的擊中特效
- ✅ 守護者之盾接觸敵人時的特效
- ✅ 穿透光束穿透敵人時的特效
- ✅ 所有特效都能正確回收，不會造成內存洩漏

## 已整合的特效位置

### 擊中特效 (hit_effect.tscn)
- 玩家投射物擊中敵人
- 穿透光束穿透敵人
- 守護者之盾接觸敵人
- 敵人投射物擊中玩家或障礙物
- 敵人近戰攻擊玩家

### 死亡特效 (death_effect.tscn)
- 所有敵人死亡時

### 升級特效 (levelup_effect.tscn)
- 玩家升級時

## 技術實現細節

### VFXManager 自動加載
- 已添加到專案的自動加載列表
- 使用節點池優化性能
- 自動管理特效的生命週期

### 節點池整合
- 所有特效都通過 NodePoolManager 管理
- 特效播放完成後自動回收
- 避免頻繁的節點創建和銷毀

### 信號系統
- 特效腳本發出 `finished` 信號
- VFXManager 自動監聽並回收特效
- 支持自定義特效的擴展
