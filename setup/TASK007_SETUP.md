# TASK007 設置指南：升級系統實現

## 概述
本指南將幫助您在 Godot 編輯器中配置 TASK007 中實現的升級系統。此任務建立了處理數據資源的「引擎」，包括創建 `WeaponManager` 來管理武器，並重構升級畫面以動態顯示和應用升級選項。

## 前置條件
確保您已完成 TASK006，並且以下資源文件存在：
- `scripts/resources/upgrade_data.gd`
- `scripts/resources/weapon_data.gd`
- `scripts/resources/ability_data.gd`

## 設置步驟

### 步驟 1: 創建 PlayerStats 資源文件

1. 在 Godot 編輯器中，右鍵點擊 `FileSystem` 面板中的 `resources` 資料夾
2. 選擇 `New Resource...`
3. 在搜尋框中輸入 `PlayerStats` 或選擇 `Script` 類型
4. 點擊 `Create`
5. 將腳本路徑設置為 `scripts/resources/player_stats.gd`
6. 點擊 `Create`
7. 將此資源保存為 `resources/player_stats.tres`

### 步驟 2: 為玩家場景添加 WeaponManager 節點

1. 打開 `scenes/player/player.tscn`
2. 在 `Player` 節點下右鍵點擊，選擇 `Add Child Node`
3. 選擇 `Node` 類型，命名為 `WeaponManager`
4. 選中 `WeaponManager` 節點
5. 在 `Inspector` 面板中，點擊 `Script` 屬性旁的箭頭
6. 選擇 `Load`，然後選擇 `scripts/managers/weapon_manager.gd`
7. 保存場景

### 步驟 3: 配置玩家的 PlayerStats

1. 在 `player.tscn` 中選中 `Player` 節點
2. 在 `Inspector` 面板中找到 `Stats` 屬性
3. 點擊 `PlayerStats` 類型旁的箭頭
4. 選擇 `Load`，然後選擇您剛才創建的 `resources/player_stats.tres`
5. 保存場景

### 步驟 4: 配置 LevelManager 的升級池

1. 打開 `scripts/managers/level_manager.gd` 腳本
2. 在 `Inspector` 面板中找到 `Upgrade Pool` 屬性
3. 點擊陣列旁的箭頭展開
4. 點擊 `Add Element` 按鈕
5. 選擇 `Load`，然後選擇 `resources/weapons/magic_missile.tres`
6. 重複步驟 4-5 來添加更多升級選項（如果有的話）
7. 保存腳本

### 步驟 5: 測試升級系統

1. 運行遊戲
2. 收集經驗值直到升級
3. 升級畫面應該會顯示從升級池中隨機選取的選項
4. 選擇一個升級選項，檢查控制台輸出
5. 驗證武器是否被正確添加或升級

## 故障排除

### 問題 1: 升級畫面沒有顯示選項
**解決方案：**
- 檢查 `LevelManager` 的 `Upgrade Pool` 是否包含資源
- 確認 `magic_missile.tres` 文件存在且配置正確
- 檢查控制台是否有錯誤訊息

### 問題 2: 武器沒有被添加
**解決方案：**
- 確認 `Player` 場景中有 `WeaponManager` 節點
- 檢查 `WeaponManager` 是否正確附加了腳本
- 驗證 `magic_missile.tres` 中的 `weapon_scene` 屬性是否設置

### 問題 3: 玩家移動速度沒有變化
**解決方案：**
- 確認 `Player` 節點的 `Stats` 屬性已設置
- 檢查 `PlayerStats` 資源文件是否正確創建
- 驗證 `player.gd` 腳本中的 `stats.move_speed` 引用

## 驗收檢查清單

- [ ] `PlayerStats` 資源文件已創建並配置
- [ ] `WeaponManager` 節點已添加到玩家場景
- [ ] `LevelManager` 的升級池已配置
- [ ] 升級畫面能顯示真實的升級選項
- [ ] 選擇升級選項後，武器能正確添加或升級
- [ ] 技能升級能正確修改玩家屬性
- [ ] 遊戲可以無錯誤運行

## 下一步

完成此設置後，您將擁有一個完整的數據驅動升級系統。接下來可以：
- 在 TASK008 中添加更多武器和技能內容
- 優化升級選項的隨機選擇邏輯
- 添加升級效果的視覺反饋

## 注意事項

- 確保所有腳本文件都正確保存
- 檢查資源文件的引用路徑是否正確
- 如果遇到編譯錯誤，檢查腳本語法
- 建議在每次修改後都保存場景和腳本
