# TASK008 設置指南：新武器與技能配置

## 概述
本指南將幫助你在 Godot 編輯器中配置 TASK008 中新創建的武器和技能，將它們添加到遊戲的升級系統中。

## 已完成的工作
✅ 守護者之盾武器場景和腳本  
✅ 穿透光束武器場景和腳本  
✅ 敏捷光環技能資源  
✅ 時間奇點技能資源  

## 需要在 Godot 編輯器中完成的配置

### 步驟 1: 檢查 LevelManager 場景
1. 在 Godot 編輯器中打開 `scenes/worlds/game_world.tscn`
2. 找到 `LevelManager` 節點
3. 在檢查器面板中，找到 `upgrade_pool` 導出變數

### 步驟 2: 添加新武器到升級池
1. 在 `upgrade_pool` 陣列中，點擊 "+" 按鈕添加新元素
2. 將以下資源文件拖拽到陣列中：
   - `res://resources/weapons/guardian_shield.tres` (守護者之盾)
   - `res://resources/weapons/piercing_beam.tres` (穿透光束)

### 步驟 3: 添加新技能到升級池
1. 繼續在 `upgrade_pool` 陣列中添加新元素
2. 將以下資源文件拖拽到陣列中：
   - `res://resources/abilities/agility_aura.tres` (敏捷光環)
   - `res://resources/abilities/temporal_singularity.tres` (時間奇點)

### 步驟 4: 配置穿透光束武器
1. 打開 `scenes/weapons/piercing_beam_weapon.tscn`
2. 選擇根節點 `PiercingBeamWeapon`
3. 在檢查器面板中，找到 `projectile_scene` 導出變數
4. 將 `res://scenes/weapons/projectiles/piercing_beam_projectile.tscn` 拖拽到這個變數中

### 步驟 5: 測試配置
1. 運行遊戲
2. 查看控制台輸出，確認升級池初始化成功
3. 升級時應該能看到新的武器和技能選項
4. 選擇新武器後，應該能在遊戲中看到它們的效果

### 步驟 6: 驗證資源文件
如果升級選項仍然沒有出現，請檢查：
1. 在 Godot 編輯器中，右鍵點擊 `resources/weapons/` 和 `resources/abilities/` 目錄
2. 選擇 "重新掃描" 確保所有資源文件被識別
3. 檢查每個 `.tres` 文件的腳本引用是否正確
4. 確認沒有腳本語法錯誤（紅色錯誤標記）

## 注意事項

### 守護者之盾
- 護盾會圍繞玩家旋轉
- 對接觸到的敵人造成傷害
- 有傷害冷卻機制，避免重複傷害

### 穿透光束
- 發射可穿透多個敵人的光束
- 有生命週期管理，避免性能問題
- 需要正確配置投射物場景引用

### 敏捷光環
- 被動提升玩家移動速度
- 效果會應用到 `PlayerStats` 資源中

### 時間奇點
- 被動減少所有武器冷卻時間
- 效果會應用到武器管理器中

## 故障排除

### 如果新武器/技能沒有出現在升級選項中
1. 檢查 `LevelManager` 的 `upgrade_pool` 陣列是否正確配置
2. 確認所有 `.tres` 資源文件路徑正確
3. 檢查腳本中是否有語法錯誤
4. 檢查控制台輸出，應該能看到升級池初始化的日誌信息
5. 如果仍然只有魔法飛彈，可能是資源文件載入失敗

### 調試步驟
1. 在 Godot 編輯器中運行遊戲
2. 查看控制台輸出，應該能看到類似以下的日誌：
   ```
   正在初始化默認升級池...
   已添加魔法飛彈到升級池
   已添加守護者之盾到升級池
   已添加穿透光束到升級池
   已添加敏捷光環到升級池
   已添加時間奇點到升級池
   升級池初始化完成，共 5 個選項
   ```
3. 如果看到 "警告：無法載入..." 的訊息，說明對應的資源文件有問題
4. 檢查資源文件的腳本引用是否正確

### 如果武器無法正常工作
1. 檢查武器場景的腳本引用是否正確
2. 確認碰撞層設置是否正確
3. 檢查是否有必要的信號連接

### 如果技能效果沒有生效
1. 檢查 `PlayerStats` 資源的導出變數
2. 確認技能選擇後的應用邏輯
3. 檢查 `LevelUpScreen` 的升級處理

## 驗收檢查清單
- [ ] 守護者之盾出現在升級選項中
- [ ] 穿透光束出現在升級選項中  
- [ ] 敏捷光環出現在升級選項中
- [ ] 時間奇點出現在升級選項中
- [ ] 選擇守護者之盾後，遊戲中出現旋轉護盾
- [ ] 選擇穿透光束後，可以發射穿透性光束
- [ ] 選擇敏捷光環後，玩家移動速度提升
- [ ] 選擇時間奇點後，武器冷卻時間減少

完成以上配置後，TASK008 就成功完成了！
