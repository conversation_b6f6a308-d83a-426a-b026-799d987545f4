# TASK005 設置指南

本文檔說明如何在 Godot 編輯器中完成 TASK005 的設置。

## 1. 自動加載設置

GameManager 已經在 `project.godot` 文件中添加為自動加載，但你需要確保它在編輯器中正確顯示：

1. 打開 Godot 編輯器
2. 進入 **專案** -> **專案設定** -> **自動加載** 標籤
3. 確認以下自動加載項目存在：
   - `LevelManager` -> `res://scripts/managers/level_manager.gd`
   - `GameManager` -> `res://scripts/managers/game_manager.gd`

如果 GameManager 沒有顯示，請手動添加：
- 點擊 **新增** 按鈕
- 節點名稱：`GameManager`
- 路徑：`res://scripts/managers/game_manager.gd`
- 點擊 **新增** 確認

## 2. 場景設置

### 2.1 確認主場景

1. 進入 **專案** -> **專案設定** -> **應用程式** 標籤
2. 確認 **主場景** 設置為：`res://main.tscn`

### 2.2 檢查場景文件

確保以下場景文件存在且正確：

1. **主場景**：`res://main.tscn` - 空的啟動場景
2. **遊戲世界**：`res://scenes/worlds/game_world.tscn` - 包含遊戲邏輯的場景
3. **主選單**：`res://scenes/ui/main_menu.tscn` - 主選單場景
4. **遊戲結束畫面**：`res://scenes/ui/game_over_screen.tscn` - 遊戲結束畫面

## 3. 腳本檢查

確保以下腳本文件存在：

1. `res://scripts/managers/game_manager.gd` - 遊戲管理器單例
2. `res://scripts/worlds/game_world.gd` - 遊戲世界控制器
3. `res://scripts/ui/main_menu.gd` - 主選單控制器
4. `res://scripts/ui/game_over_screen.gd` - 遊戲結束畫面控制器

## 4. 測試流程

### 4.1 基本功能測試

1. **啟動遊戲**：
   - 運行 `main.tscn`
   - 應該自動顯示主選單

2. **開始遊戲**：
   - 點擊 "開始遊戲" 按鈕
   - 應該切換到遊戲世界場景
   - 玩家應該出現在螢幕中心
   - 敵人應該開始生成

3. **遊戲結束**：
   - 讓玩家被敵人擊中死亡
   - 應該顯示遊戲結束畫面
   - 遊戲應該暫停

4. **返回主選單**：
   - 點擊 "返回主選單" 按鈕
   - 應該回到主選單
   - 遊戲應該恢復正常

5. **退出遊戲**：
   - 點擊 "退出遊戲" 按鈕
   - 應該關閉應用程式

## 5. 故障排除

### 5.1 常見問題

1. **GameManager 未找到錯誤**：
   - 確認 `project.godot` 中的自動加載設置正確
   - 重新啟動 Godot 編輯器

2. **場景切換失敗**：
   - 確認所有場景文件路徑正確
   - 檢查場景文件的 UID 是否唯一

3. **遊戲暫停問題**：
   - 確認 `game_over_screen.tscn` 的 `process_mode` 設置為 `3` (When Paused)
   - 檢查 UI 節點的設置

4. **玩家死亡不觸發**：
   - 確認 `player.tscn` 中的 `HealthComponent` 已正確連接
   - 檢查 `HealthComponent` 的 `died` 信號是否連接到 `player.gd`

### 5.2 調試技巧

1. **查看控制台輸出**：
   - 在 Godot 編輯器中打開 **調試器** 標籤
   - 查看是否有錯誤訊息

2. **檢查節點樹**：
   - 在場景樹中確認所有必要的節點都存在
   - 檢查信號連接是否正確

3. **測試個別場景**：
   - 單獨運行每個場景進行測試
   - 確認每個場景都能正常加載

## 6. 完成確認

完成 TASK005 後，你應該能夠：

- [x] 遊戲啟動時顯示主選單
- [x] 點擊 "開始遊戲" 按鈕會切換到遊戲世界
- [x] 玩家可以被傷害殺死
- [x] 玩家死亡時顯示遊戲結束畫面
- [x] 點擊 "返回主選單" 按鈕會回到主選單
- [x] 點擊 "退出遊戲" 按鈕會關閉應用程式
- [x] 所有場景切換都流暢，沒有殘留的舊場景節點

如果所有項目都已經完成，恭喜你！TASK005 已經成功實現。
