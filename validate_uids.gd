extends Node

func _ready():
	print("開始驗證 UID 引用...")
	validate_scene_files()
	validate_resource_files()
	print("UID 驗證完成！")

func validate_scene_files():
	print("驗證場景文件...")
	
	# 檢查 magic_missile_weapon.tscn
	var weapon_scene = load("res://scenes/weapons/magic_missile_weapon.tscn")
	if weapon_scene:
		print("✅ magic_missile_weapon.tscn 載入成功")
	else:
		print("❌ magic_missile_weapon.tscn 載入失敗")
	
	# 檢查 projectile.tscn
	var projectile_scene = load("res://scenes/weapons/projectile.tscn")
	if projectile_scene:
		print("✅ projectile.tscn 載入成功")
	else:
		print("❌ projectile.tscn 載入失敗")
	
	# 檢查 test_scene.tscn
	var test_scene = load("res://scenes/test_scene.tscn")
	if test_scene:
		print("✅ test_scene.tscn 載入成功")
	else:
		print("❌ test_scene.tscn 載入失敗")

func validate_resource_files():
	print("驗證資源文件...")
	
	# 檢查 magic_missile.tres
	var weapon_resource = load("res://resources/weapons/magic_missile.tres")
	if weapon_resource:
		print("✅ magic_missile.tres 載入成功")
		print("   - ID: ", weapon_resource.id)
		print("   - 名稱: ", weapon_resource.upgrade_name)
		print("   - 描述: ", weapon_resource.description)
		print("   - 武器場景: ", weapon_resource.weapon_scene)
		print("   - 等級統計: ", weapon_resource.stats_per_level)
	else:
		print("❌ magic_missile.tres 載入失敗")
