[gd_resource type="Resource" script_class="WeaponData" load_steps=2 format=3 uid="uid://hupxlhvwfbqe"]

[ext_resource type="Script" path="res://scripts/resources/weapon_data.gd" id="1_0x0x0"]

[resource]
script = ExtResource("1_0x0x0")
id = "guardian_shield"
upgrade_name = "守護者之盾"
description = "圍繞玩家旋轉的防護盾，對接觸到的敵人造成傷害"
scene_path = "res://scenes/weapons/guardian_shield.tscn"
base_damage = 10.0
base_cooldown = 0.0
base_range = 100.0
stats_per_level = {
"1": {"damage": 10.0, "rotation_speed": 180.0, "radius": 100.0},
"2": {"damage": 15.0, "rotation_speed": 210.0, "radius": 110.0},
"3": {"damage": 22.5, "rotation_speed": 240.0, "radius": 120.0},
"4": {"damage": 30.0, "rotation_speed": 270.0, "radius": 130.0},
"5": {"damage": 39.0, "rotation_speed": 351.0, "radius": 140.0}
}
