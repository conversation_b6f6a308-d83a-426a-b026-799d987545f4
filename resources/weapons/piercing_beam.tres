[gd_resource type="Resource" script_class="WeaponData" load_steps=2 format=3 uid="uid://5c4gcmd71f0b"]

[ext_resource type="Script" path="res://scripts/resources/weapon_data.gd" id="1_0x0x0"]

[resource]
script = ExtResource("1_0x0x0")
id = "piercing_beam"
upgrade_name = "穿透光束"
description = "發射可穿透多個敵人的光束，造成持續傷害"
scene_path = "res://scenes/weapons/piercing_beam_weapon.tscn"
base_damage = 15.0
base_cooldown = 1.5
base_range = 500.0
stats_per_level = {
"1": {"damage": 15.0, "cooldown": 1.5, "max_pierces": 3, "speed": 300.0},
"2": {"damage": 20.0, "cooldown": 1.4, "max_pierces": 4, "speed": 350.0},
"3": {"damage": 25.0, "cooldown": 1.3, "max_pierces": 5, "speed": 400.0},
"4": {"damage": 30.0, "cooldown": 1.2, "max_pierces": 6, "speed": 450.0},
"5": {"damage": 35.0, "cooldown": 1.1, "max_pierces": 7, "speed": 500.0}
}
