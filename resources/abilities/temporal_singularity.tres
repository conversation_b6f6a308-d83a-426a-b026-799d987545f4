[gd_resource type="Resource" script_class="AbilityData" load_steps=2 format=3 uid="uid://dxili7g8fxled"]

[ext_resource type="Script" path="res://scripts/resources/ability_data.gd" id="1_0x0x0"]

[resource]
script = ExtResource("1_0x0x0")
id = "temporal_singularity"
upgrade_name = "時間奇點"
description = "所有武器的冷卻時間減少 8%"
icon_path = ""
stat_bonuses = {
"cooldown_multiplier": 0.92
}
max_level = 5
level_descriptions = {
"1": "所有武器的冷卻時間減少 8%",
"2": "所有武器的冷卻時間減少 16%",
"3": "所有武器的冷卻時間減少 24%",
"4": "所有武器的冷卻時間減少 32%",
"5": "所有武器的冷卻時間減少 40%"
}
