extends CharacterBody2D

## A basic player character that can move in 8 directions.
## Handles user input for movement and applies physics-based movement.

## 玩家統計數據資源
@export var stats: PlayerStats

## 武器管理器節點
@onready var weapon_manager: WeaponManager = $WeaponManager

var _health_component: Node

func _ready() -> void:
	# 檢查並初始化 PlayerStats
	if not stats:
		print("警告：PlayerStats 未設置，使用預設值")
		stats = PlayerStats.new()
	
	# 連接健康條到健康組件
	_health_component = find_child("HealthComponent")
	var health_bar: Node = find_child("HealthBar")
	if _health_component != null and health_bar != null:
		health_bar.connect_to_health_component(_health_component)
	
	# 連接拾取區域信號
	var pickup_area: Area2D = find_child("PickupArea")
	if pickup_area != null:
		if not pickup_area.area_entered.is_connected(_on_pickup_area_area_entered):
			pickup_area.area_entered.connect(_on_pickup_area_area_entered)
	
	# 連接升級選擇信號
	var level_up_screen = get_tree().get_first_node_in_group("level_up_screen")
	if level_up_screen != null:
		if not level_up_screen.upgrade_selected.is_connected(_on_upgrade_selected):
			level_up_screen.upgrade_selected.connect(_on_upgrade_selected)
	else:
		print("警告：LevelUpScreen 未找到")

	# Add player to group for enemies to find
	add_to_group("player_group") # Changed from "player" to "player_group" as per TASK002
	# Connect health component 'died' signal
	if _health_component:
		if not _health_component.died.is_connected(_on_health_component_died):
			_health_component.died.connect(_on_health_component_died)


func apply_permanent_upgrades(permanent_stats: PermanentStats):
	if not stats:
		printerr("Player has no stats resource to apply upgrades to!")
		return

	# IMPORTANT: We modify the INSTANCED resource, not the base file.
	# This ensures that changes are only applied to this specific player instance
	# and don't modify the original PlayerStats .tres file on disk.
	if stats.resource_path != "": # Check if it's a loaded resource
		stats = stats.duplicate() # Duplicate if it's a loaded resource

	stats.max_health += permanent_stats.bonus_health
	stats.move_speed += permanent_stats.bonus_move_speed
	stats.damage_multiplier += permanent_stats.bonus_damage_multiplier
	# Cooldown is reductive, so we subtract the bonus
	stats.cooldown_multiplier -= permanent_stats.bonus_cooldown_multiplier

	# We need to re-initialize the health component with the new max health
	if _health_component:
		_health_component.max_health = stats.max_health
		_health_component.current_health = stats.max_health # Heal to full at start of run
	
	print("Permanent upgrades applied. New max health: %d, New move speed: %.1f" % [stats.max_health, stats.move_speed])


func _physics_process(_delta: float) -> void:
	_handle_movement()

func _handle_movement() -> void:
	# 獲取輸入方向
	var input_direction := Vector2.ZERO
	input_direction.x = Input.get_action_strength("move_right") - Input.get_action_strength("move_left")
	input_direction.y = Input.get_action_strength("move_down") - Input.get_action_strength("move_up")
	
	# 標準化以防止對角線移動更快
	input_direction = input_direction.normalized()
	
	# 設置速度並應用移動（從 stats 讀取移動速度）
	var current_move_speed = stats.move_speed if stats else 300.0
	velocity = input_direction * current_move_speed
	move_and_slide()

func _on_pickup_area_area_entered(area: Area2D) -> void:
	if area.is_in_group("experience_orbs"):
		# Connect to the experience orb's collected signal if not already connected
		if not area.collected.is_connected(_on_experience_orb_collected):
			area.collected.connect(_on_experience_orb_collected)
		area.collect()

func _on_experience_orb_collected(amount: int) -> void:
	# Add experience to the LevelManager
	if LevelManager != null:
		LevelManager.add_experience(amount)

func _on_health_component_died() -> void:
	"""處理玩家死亡事件"""
	print("Game Over! Player died!")
	# 調用 GameManager 的 end_game 方法
	if GameManager != null:
		GameManager.end_game()
	else:
		push_error("GameManager 單例未找到")

## 處理升級選擇
func _on_upgrade_selected(upgrade_data: Resource) -> void:
	if upgrade_data:
		apply_upgrade(upgrade_data)
	else:
		print("錯誤：升級數據為空")

## 應用升級
func apply_upgrade(upgrade_data: Resource) -> void:
	if not upgrade_data:
		return
	
	print("正在應用升級：", upgrade_data.upgrade_name)
	
	# 根據升級類型應用不同的效果
	if upgrade_data is WeaponData:
		# 武器升級
		if weapon_manager:
			weapon_manager.add_weapon(upgrade_data)
		else:
			print("錯誤：WeaponManager 未找到")
	
	elif upgrade_data is AbilityData:
		# 技能升級
		if stats:
			_apply_ability_upgrade(upgrade_data)
		else:
			print("錯誤：PlayerStats 未找到")
	
	else:
		print("警告：未知的升級類型：", upgrade_data.get_class())

## 應用技能升級
func _apply_ability_upgrade(ability_data: AbilityData) -> void:
	if not ability_data or not stats:
		return
	
	# 遍歷技能提供的屬性加成
	for stat_name in ability_data.stat_bonuses.keys():
		var bonus_value = ability_data.stat_bonuses[stat_name]
		
		# 檢查是否為倍數加成（通常以 "_multiplier" 結尾）
		var is_multiplier = stat_name.ends_with("_multiplier")
		
		# 應用屬性加成
		stats.apply_stat_bonus(stat_name, bonus_value, is_multiplier)
		print("已應用技能加成：", stat_name, " = ", bonus_value)
	
	# 增加技能堆疊數
	if ability_data.can_add_stack():
		ability_data.add_stack()
		print("技能堆疊數增加：", ability_data.current_stacks)
	else:
		print("技能已達到最大堆疊數")
