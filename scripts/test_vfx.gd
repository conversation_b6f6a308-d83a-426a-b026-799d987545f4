extends Node2D

## VFX 系統測試腳本
## 用於測試視覺特效系統是否正常工作

func _ready() -> void:
	print("VFX 測試腳本已啟動")
	
	# 等待一秒後開始測試
	await get_tree().create_timer(1.0).timeout
	test_vfx_system()

func test_vfx_system() -> void:
	print("開始測試 VFX 系統...")
	
	# 檢查 VFXManager 是否存在
	var vfx_manager = get_node_or_null("/root/VFXManager")
	if vfx_manager == null:
		print("❌ VFXManager 未找到！請檢查自動加載設置。")
		return
	else:
		print("✅ VFXManager 已找到")
	
	# 測試擊中特效
	print("測試擊中特效...")
	if vfx_manager.has_method("play_hit_effect"):
		vfx_manager.play_hit_effect(Vector2(100, 100))
		print("✅ 擊中特效已觸發")
	else:
		print("❌ play_hit_effect 方法未找到")
	
	# 等待 2 秒後測試死亡特效
	await get_tree().create_timer(2.0).timeout
	print("測試死亡特效...")
	if vfx_manager.has_method("play_death_effect"):
		vfx_manager.play_death_effect(Vector2(200, 200))
		print("✅ 死亡特效已觸發")
	else:
		print("❌ play_death_effect 方法未找到")
	
	# 等待 2 秒後測試升級特效
	await get_tree().create_timer(2.0).timeout
	print("測試升級特效...")
	if vfx_manager.has_method("play_levelup_effect"):
		vfx_manager.play_levelup_effect(Vector2(300, 300))
		print("✅ 升級特效已觸發")
	else:
		print("❌ play_levelup_effect 方法未找到")
	
	print("VFX 系統測試完成！")

func _input(event: InputEvent) -> void:
	# 按空白鍵重新測試
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_SPACE:
			print("重新測試 VFX 系統...")
			test_vfx_system()
		elif event.keycode == KEY_1:
			# 測試擊中特效
			var vfx_manager = get_node_or_null("/root/VFXManager")
			if vfx_manager:
				vfx_manager.play_hit_effect(get_global_mouse_position())
		elif event.keycode == KEY_2:
			# 測試死亡特效
			var vfx_manager = get_node_or_null("/root/VFXManager")
			if vfx_manager:
				vfx_manager.play_death_effect(get_global_mouse_position())
		elif event.keycode == KEY_3:
			# 測試升級特效
			var vfx_manager = get_node_or_null("/root/VFXManager")
			if vfx_manager:
				vfx_manager.play_levelup_effect(get_global_mouse_position())
