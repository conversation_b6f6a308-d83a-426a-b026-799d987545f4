extends Node2D
class_name EnemySpawner

## 敵人生成器
## 根據配置生成不同類型的敵人，並能生成Boss。

@export var spawn_radius: float = 100.0  # 生成範圍
@export var max_enemies: int = 10  # 最大敵人數量
@export var spawn_interval: float = 3.0  # 生成間隔
@export var difficulty_curve: Curve  # 難度曲線

# 敵人類型配置
@export var enemy_scenes: Dictionary = {
	"tracker": preload("res://scenes/enemies/tracker_enemy.tscn"),
	"shooter": preload("res://scenes/enemies/shooter_enemy.tscn"),
	"charger": preload("res://scenes/enemies/charger_enemy.tscn")
}
@export var boss_scene: PackedScene

# 敵人數據配置
@export var enemy_data_resources: Dictionary = {
	"tracker": null,
	"shooter": null,
	"charger": null
}

var _spawn_timer: float = 0.0
var _current_enemy_count: int = 0
var _spawned_enemies: Array[Node2D] = []
var _player: Node2D
var _game_manager: Node
var _difficulty_manager: Node
@onready var _game_hud: CanvasLayer = get_node_or_null("../GameHUD")

func _ready() -> void:
	# 尋找玩家和遊戲管理器
	_player = get_tree().get_first_node_in_group("player_group")
	_game_manager = get_tree().get_first_node_in_group("game_manager")
	_difficulty_manager = get_tree().get_first_node_in_group("difficulty_manager")
	
	# 延遲初始化敵人生成，確保場景完全加載
	call_deferred("_initialize_enemy_spawning")

func _process(delta: float) -> void:
	if _player == null or _game_manager == null:
		return
	
	# 根據難度調整生成參數
	_adjust_spawning_by_difficulty()
	
	# 更新生成計時器
	_spawn_timer += delta
	
	# 檢查是否需要生成敵人
	if _spawn_timer >= spawn_interval and _current_enemy_count < max_enemies:
		_spawn_enemy()
		_spawn_timer = 0.0
	
	# 清理已死亡的敵人
	_cleanup_dead_enemies()

func _initialize_enemy_spawning() -> void:
	# 創建預設的敵人數據資源
	_create_default_enemy_data()
	
	# 等待一幀後再生成初始敵人，確保場景完全準備好
	await get_tree().process_frame
	
	# 生成初始敵人
	for i in range(3):
		_spawn_enemy()

func _create_default_enemy_data() -> void:
	# 只在沒有設置敵人數據資源時才創建預設數據
	if enemy_data_resources["tracker"] == null:
		# 創建追蹤型敵人數據
		var tracker_data = EnemyData.new()
		tracker_data.enemy_type = EnemyData.EnemyType.TRACKER
		tracker_data.max_health = 80.0
		tracker_data.move_speed = 180.0
		tracker_data.damage = 15.0
		tracker_data.damage_cooldown = 1.5
		tracker_data.detection_range = 350.0
		tracker_data.attack_range = 40.0
		tracker_data.experience_value = 12
		enemy_data_resources["tracker"] = tracker_data
	
	if enemy_data_resources["shooter"] == null:
		# 創建射擊型敵人數據
		var shooter_data = EnemyData.new()
		shooter_data.enemy_type = EnemyData.EnemyType.SHOOTER
		shooter_data.max_health = 60.0
		shooter_data.move_speed = 100.0
		shooter_data.damage = 20.0
		shooter_data.damage_cooldown = 2.0
		shooter_data.detection_range = 400.0
		shooter_data.attack_range = 200.0
		shooter_data.experience_value = 15
		enemy_data_resources["shooter"] = shooter_data
	
	if enemy_data_resources["charger"] == null:
		# 創建衝鋒型敵人數據
		var charger_data = EnemyData.new()
		charger_data.enemy_type = EnemyData.EnemyType.CHARGER
		charger_data.max_health = 120.0
		charger_data.move_speed = 250.0
		charger_data.damage = 25.0
		charger_data.damage_cooldown = 3.0
		charger_data.detection_range = 300.0
		charger_data.attack_range = 60.0
		charger_data.experience_value = 18
		enemy_data_resources["charger"] = charger_data

func _spawn_enemy() -> void:
	if _player == null:
		push_error("無法生成敵人：玩家節點為空")
		return
	
	# 選擇敵人類型
	var enemy_type = _select_enemy_type()
	var enemy_scene = enemy_scenes[enemy_type]
	var enemy_data = enemy_data_resources[enemy_type]
	
	print("嘗試生成敵人：", enemy_type)
	print("敵人場景：", enemy_scene)
	print("敵人數據：", enemy_data)
	
	if enemy_scene == null or enemy_data == null:
		push_error("無法生成敵人：場景或數據為空")
		return
	
	# 計算生成位置
	var spawn_position = _calculate_spawn_position()
	print("生成位置：", spawn_position)
	
	# 從節點池取得敵人
	var enemy: Node2D = NodePoolManager.acquire(enemy_scene)
	enemy.global_position = spawn_position
	
	# 設置敵人數據
	if enemy.has_method("set_enemy_data"):
		enemy.set_enemy_data(enemy_data)
		print("已設置敵人數據")
	else:
		print("敵人沒有set_enemy_data方法")
	
	# 根據敵人類型設置特定屬性
	_setup_enemy_by_type(enemy, enemy_type)
	
	# 添加到場景 - 使用更可靠的方式
	var parent_scene = get_parent()
	print("父場景：", parent_scene)
	
	if parent_scene != null:
		parent_scene.add_child(enemy)
		print("成功添加敵人到場景")
		
		# 添加到敵人列表
		_spawned_enemies.append(enemy)
		_current_enemy_count += 1
		
		# 連接死亡信號
		var health_component = enemy.find_child("HealthComponent")
		if health_component and health_component.has_signal("died"):
			if not health_component.died.is_connected(_on_enemy_died.bind(enemy)):
				health_component.died.connect(_on_enemy_died.bind(enemy))
				print("已連接死亡信號")
		else:
			print("未找到HealthComponent或died信號")
		
		print("生成敵人成功：", enemy_type, " 在位置：", spawn_position)
	else:
		push_error("無法添加敵人到場景：找不到父節點")
		enemy.queue_free()  # 清理實例化的敵人

func _select_enemy_type() -> String:
	# 根據難度和隨機性選擇敵人類型
	var rand_value = randf()
	
	# 基礎機率分配
	if rand_value < 0.5:
		return "tracker"  # 50% 機率生成追蹤型
	elif rand_value < 0.8:
		return "shooter"  # 30% 機率生成射擊型
	else:
		return "charger"  # 20% 機率生成衝鋒型

func _calculate_spawn_position() -> Vector2:
	if _player == null:
		return global_position
	
	# 在遊戲世界外圍生成敵人，而不是在玩家附近
	var viewport_size = get_viewport().get_visible_rect().size
	var world_width = viewport_size.x
	var world_height = viewport_size.y
	
	# 隨機選擇生成邊緣（上、下、左、右）
	var edge = randi() % 4
	var spawn_pos: Vector2
	
	match edge:
		0:  # 上邊緣
			spawn_pos.x = randf_range(0, world_width)
			spawn_pos.y = -50  # 稍微超出上邊界
		1:  # 右邊緣
			spawn_pos.x = world_width + 50  # 稍微超出右邊界
			spawn_pos.y = randf_range(0, world_height)
		2:  # 下邊緣
			spawn_pos.x = randf_range(0, world_width)
			spawn_pos.y = world_height + 50  # 稍微超出下邊界
		3:  # 左邊緣
			spawn_pos.x = -50  # 稍微超出左邊界
			spawn_pos.y = randf_range(0, world_height)
	
	# 確保生成位置在合理的遊戲世界範圍內
	spawn_pos.x = clamp(spawn_pos.x, -100, world_width + 100)
	spawn_pos.y = clamp(spawn_pos.y, -100, world_height + 100)
	
	print("敵人生成位置：", spawn_pos, "，邊緣：", edge)
	return spawn_pos

func _setup_enemy_by_type(enemy: Node2D, enemy_type: String) -> void:
	match enemy_type:
		"tracker":
			# 追蹤型敵人不需要額外設置
			pass
			
		"shooter":
			# 射擊型敵人設置投射物場景
			if enemy.has_method("set_projectile_scene"):
				var projectile_scene = preload("res://scenes/weapons/enemy_projectile.tscn")
				enemy.set_projectile_scene(projectile_scene)
			
			# 設置射擊屬性
			if enemy.has_method("set_attack_range"):
				enemy.set_attack_range(150.0, 250.0)
			
			if enemy.has_method("set_burst_properties"):
				enemy.set_burst_properties(3, 0.1)
			
		"charger":
			# 衝鋒型敵人設置衝鋒屬性
			if enemy.has_method("set_charge_properties"):
				enemy.set_charge_properties(400.0, 1.0, 3.0, 35.0)
			
			if enemy.has_method("set_stun_duration"):
				enemy.set_stun_duration(1.5)
	
	# 應用難度調整
	_apply_difficulty_to_enemy(enemy)
	
	# 打印調試信息
	print("設置敵人類型：", enemy_type, "，節點：", enemy.name)

func _adjust_spawning_by_difficulty() -> void:
	"""根據難度調整敵人生成參數"""
	if _difficulty_manager == null:
		return
	
	# 調整生成間隔
	var adjusted_interval = spawn_interval / _difficulty_manager.get_spawn_interval_multiplier()
	spawn_interval = adjusted_interval
	
	# 調整最大敵人數量
	var adjusted_max = int(max_enemies * _difficulty_manager.get_max_enemies_multiplier())
	max_enemies = adjusted_max

func _apply_difficulty_to_enemy(enemy: Node2D) -> void:
	"""將難度調整應用到敵人上"""
	if _difficulty_manager == null or enemy == null:
		return
	
	# 獲取敵人的數據資源
	var enemy_data = null
	if enemy.has_method("get_enemy_data"):
		enemy_data = enemy.get_enemy_data()
	
	# 如果沒有數據資源，嘗試從導出變數獲取
	if enemy_data == null and enemy.has_method("get_enemy_type"):
		var enemy_type = enemy.get_enemy_type()
		enemy_data = EnemyData.get_default_data(enemy_type)
	
	# 應用難度調整
	if enemy_data != null:
		var adjusted_data = _difficulty_manager.apply_difficulty_to_enemy_data(enemy_data)
		if enemy.has_method("set_enemy_data"):
			enemy.set_enemy_data(adjusted_data)

func _on_enemy_died(enemy: Node2D) -> void:
	# Check if the dying enemy is the boss
	if enemy is Boss:
		if _game_hud and _game_hud.has_method("hide_boss_health_bar"):
			_game_hud.hide_boss_health_bar()
			print("Boss died, hiding health bar.")

	# 敵人死亡時從列表中移除
	if enemy in _spawned_enemies:
		# Disconnect signals to prevent memory leaks
		var health_component = enemy.find_child("HealthComponent")
		if health_component:
			var signal_name = "died"
			if health_component is BossHealthComponent:
				signal_name = "boss_defeated"
			
			if health_component.has_signal(signal_name) and health_component.get(signal_name).is_connected(_on_enemy_died.bind(enemy)):
				health_component.get(signal_name).disconnect(_on_enemy_died.bind(enemy))

		_spawned_enemies.erase(enemy)
		_current_enemy_count -= 1
		print("敵人死亡，從列表中移除，當前敵人數量：", _current_enemy_count)

func _cleanup_dead_enemies() -> void:
	# 清理已死亡的敵人
	for i in range(_spawned_enemies.size() - 1, -1, -1):
		var enemy = _spawned_enemies[i]
		if enemy == null or not is_instance_valid(enemy):
			_spawned_enemies.remove_at(i)
			_current_enemy_count -= 1

# 公共方法
func spawn_boss() -> void:
	if boss_scene == null:
		push_error("Boss scene is not set in the EnemySpawner!")
		return
	
	print("Spawning Boss...")
	var boss_instance = boss_scene.instantiate() as Node2D
	
	var spawn_position = _calculate_spawn_position()
	boss_instance.global_position = spawn_position
	
	var parent_scene = get_parent()
	if parent_scene:
		parent_scene.add_child(boss_instance)
		_spawned_enemies.append(boss_instance)
		_current_enemy_count += 1
		print("Boss spawned successfully at %s" % str(spawn_position))
	else:
		push_error("Could not spawn boss: parent scene not found.")
		boss_instance.queue_free()
		return

	# Connect health bar
	if _game_hud and _game_hud.has_method("show_boss_health_bar"):
		var boss_health_component = boss_instance.find_child("BossHealthComponent")
		if boss_health_component:
			_game_hud.show_boss_health_bar(boss_health_component)
			print("Boss health bar connected to HUD.")
		else:
			push_error("Could not find BossHealthComponent on the spawned boss.")
	else:
		push_error("Could not find GameHUD or it's missing the show_boss_health_bar method.")

	# Connect death signal
	var health_component = boss_instance.find_child("BossHealthComponent")
	if health_component and health_component.has_signal("boss_defeated"):
		if not health_component.boss_defeated.is_connected(_on_enemy_died.bind(boss_instance)):
			health_component.boss_defeated.connect(_on_enemy_died.bind(boss_instance))
			print("Connected boss death signal.")

func set_spawn_interval(interval: float) -> void:
	spawn_interval = interval

func set_max_enemies(max_count: int) -> void:
	max_enemies = max_count

func get_current_enemy_count() -> int:
	return _current_enemy_count

func get_spawned_enemies() -> Array[Node2D]:
	return _spawned_enemies

func clear_all_enemies() -> void:
	# 清除所有敵人（回收到池）
	for enemy in _spawned_enemies:
		if enemy != null and is_instance_valid(enemy):
			var pool := get_tree().root.get_node_or_null("NodePoolManager")
			if pool:
				pool.return_node(enemy)
			else:
				enemy.queue_free()
	_spawned_enemies.clear()
	_current_enemy_count = 0

func pause_spawning() -> void:
	# 暫停敵人生成
	set_process(false)

func resume_spawning() -> void:
	# 恢復敵人生成
	set_process(true)

# 波次計時器處理
func _on_wave_timer_timeout() -> void:
	# 波次計時器超時時生成敵人
	if _current_enemy_count < max_enemies:
		_spawn_enemy()
