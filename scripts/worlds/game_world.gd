extends Node2D

## 遊戲世界控制器，處理遊戲世界的初始化和管理

@onready var enemy_spawner: EnemySpawner = $EnemySpawner
@onready var boss_spawn_test_timer: Timer = $BossSpawnTestTimer

func _ready() -> void:
	# 設置玩家位置到螢幕中心
	_set_player_to_center()
	
	# 確保遊戲沒有暫停
	get_tree().paused = false

	# Start the test timer to spawn the boss
	boss_spawn_test_timer.start()

func _set_player_to_center() -> void:
	# 獲取玩家節點
	var player: Node2D = find_child("Player")
	if player == null:
		push_error("在 GameWorld 場景中找不到 Player 節點")
		return
	
	# 獲取視口大小
	var viewport_size: Vector2 = get_viewport().get_visible_rect().size
	
	# 計算中心位置
	var center_position: Vector2 = viewport_size * 0.5
	
	# 設置玩家位置到中心
	player.global_position = center_position

func _on_boss_spawn_test_timer_timeout() -> void:
	if enemy_spawner:
		enemy_spawner.spawn_boss()
