extends Node2D
class_name GuardianShield

@export var rotation_speed: float = 180.0  # 度/秒
@export var damage: float = 10.0
@export var radius: float = 100.0

var damaged_enemies: Array[Node] = []
var damage_cooldown: float = 0.5

func _ready():
	# 設置護盾的初始位置
	$Shield.position = Vector2(radius, 0)
	
	# 連接信號
	$Shield.body_entered.connect(_on_shield_body_entered)
	$Shield/DamageTimer.timeout.connect(_on_damage_timer_timeout)

func _process(delta):
	# 讓護盾圍繞玩家旋轉
	$Shield.rotate(deg_to_rad(rotation_speed * delta))

func _on_shield_body_entered(body: Node):
	if body.has_method("take_damage") and body not in damaged_enemies:
		# 對敵人造成傷害
		body.take_damage(damage)

		# 播放擊中特效
		var vfx_manager = get_node_or_null("/root/VFXManager")
		if vfx_manager:
			vfx_manager.play_hit_effect(body.global_position)

		# 將敵人添加到已傷害列表，避免重複傷害
		damaged_enemies.append(body)

		# 啟動傷害計時器
		$Shield/DamageTimer.start()

func _on_damage_timer_timeout():
	# 計時器結束後，清空已傷害敵人列表，允許再次傷害
	damaged_enemies.clear()

func set_level(level: int):
	# 根據等級調整屬性
	damage = 10.0 + (level - 1) * 5.0
	rotation_speed = 180.0 + (level - 1) * 30.0
	
	# 高等級時可以增加護盾數量（這裡簡化為增加傷害和速度）
	if level >= 3:
		damage *= 1.5
	if level >= 5:
		rotation_speed *= 1.3
