extends Area2D
class_name PiercingBeamProjectile

@export var speed: float = 300.0
@export var damage: float = 15.0
@export var max_pierces: int = 3

var direction: Vector2 = Vector2.RIGHT
var pierced_enemies: Array[Node] = []
var current_pierces: int = 0

func _ready():
	# 連接信號（避免重複連接）
	if not body_entered.is_connected(_on_body_entered):
		body_entered.connect(_on_body_entered)
	var life = $LifeTimer
	if life and not life.timeout.is_connected(_on_life_timer_timeout):
		life.timeout.connect(_on_life_timer_timeout)
	var vis = $VisibilityNotifier2D
	if vis and not vis.screen_exited.is_connected(_on_screen_exited):
		vis.screen_exited.connect(_on_screen_exited)

func _physics_process(delta):
	# 移動投射物
	position += direction * speed * delta

func _on_body_entered(body: Node):
	if body.has_method("take_damage") and body not in pierced_enemies:
		# 對敵人造成傷害
		body.take_damage(damage)
		
		# 將敵人添加到已穿透列表
		pierced_enemies.append(body)
		current_pierces += 1
		
		# 如果達到最大穿透次數，回收到池
		if current_pierces >= max_pierces:
			_return_to_pool()

func _on_life_timer_timeout():
	# 生命計時器結束，回收到池
	_return_to_pool()

func _on_screen_exited():
	# 離開螢幕，回收到池
	_return_to_pool()

func set_direction(new_direction: Vector2):
	direction = new_direction.normalized()
	# 根據方向調整投射物的旋轉
	rotation = direction.angle()

func set_level(level: int):
	# 根據等級調整屬性
	damage = 15.0 + (level - 1) * 5.0
	max_pierces = 3 + (level - 1)
	speed = 300.0 + (level - 1) * 50.0

# 節點池回調與回收
func on_taken_from_pool() -> void:
	pierced_enemies.clear()
	current_pierces = 0
	visible = true
	set_deferred("monitoring", true)

func on_returned_to_pool() -> void:
	visible = false
	set_deferred("monitoring", false)

func _return_to_pool() -> void:
	if Engine.has_singleton("NodePoolManager"):
		NodePoolManager.return_node(self)
	else:
		queue_free()
