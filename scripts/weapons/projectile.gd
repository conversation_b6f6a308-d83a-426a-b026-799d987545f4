extends Area2D

## A basic projectile that moves forward and damages enemies.
## Now pooled via NodePoolManager to avoid frequent allocations.

@export var damage: float = 5.0
@export var speed: float = 500.0
@export var max_distance: float = 1000.0

var _distance_traveled: float = 0.0
var _velocity: Vector2

func _ready() -> void:
	# Connect once
	if not body_entered.is_connected(_on_body_entered):
		body_entered.connect(_on_body_entered)
	# Initialize velocity for non-pooled first spawn
	_velocity = Vector2.RIGHT.rotated(rotation) * speed

func _physics_process(delta: float) -> void:
	var movement: Vector2 = _velocity * delta
	global_position += movement
	_distance_traveled += movement.length()
	if _distance_traveled >= max_distance:
		_return_to_pool()

func _on_body_entered(body: Node2D) -> void:
	# Unified damage handling.
	if body.has_method("take_damage"):
		body.take_damage(damage)
	else:
		var health_component: Node = body.find_child("HealthComponent")
		if health_component and health_component.has_method("take_damage"):
			health_component.take_damage(damage)
	# 播放命中音效（若已設定）
	var am = get_node_or_null("/root/AudioManager")
	if am and am.hit_sfx:
		am.play_sfx(am.hit_sfx)
	_return_to_pool()

func on_taken_from_pool() -> void:
	# Reset runtime state when re-used
	_distance_traveled = 0.0
	_velocity = Vector2.RIGHT.rotated(rotation) * speed
	set_deferred("monitoring", true)

func on_returned_to_pool() -> void:
	# Optional: disable monitoring to save cost while pooled
	set_deferred("monitoring", false)

func _return_to_pool() -> void:
	var tree := get_tree()
	if tree:
		var pool := tree.root.get_node_or_null("NodePoolManager")
		if pool:
			pool.return_node(self)
			return
	queue_free()
