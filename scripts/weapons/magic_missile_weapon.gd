extends Node

## Magic Missile weapon that automatically fires projectiles towards the nearest enemy.
## Implements a cooldown timer to control firing rate.

@export var projectile_scene: PackedScene
@export var fire_rate: float = 0.5  # Time between shots in seconds
@export var detection_range: float = 500.0  # Range to detect enemies

@onready var _cooldown_timer: Timer

func _ready() -> void:
	# Create and configure the cooldown timer
	_cooldown_timer = Timer.new()
	_cooldown_timer.wait_time = fire_rate
	_cooldown_timer.autostart = true
	_cooldown_timer.timeout.connect(_on_cooldown_timer_timeout)
	add_child(_cooldown_timer)

func fire() -> void:
	if projectile_scene == null:
		push_error("Projectile scene not assigned to MagicMissileWeapon")
		return
	
	# Find the nearest enemy
	var nearest_enemy: Node2D = _find_nearest_enemy()
	if nearest_enemy == null:
		return  # No enemy to target
	
	# Get projectile from pool
	var projectile: Node = NodePoolManager.acquire(projectile_scene)
	# 播放武器開火音效（若有）
	var am = get_node_or_null("/root/AudioManager")
	if am and am.weapon_fire_sfx:
		am.play_sfx(am.weapon_fire_sfx)

	# Set projectile position to player's position
	var player_position: Vector2 = _get_player_position()
	projectile.global_position = player_position

	# Calculate direction to nearest enemy
	var direction: Vector2 = player_position.direction_to(nearest_enemy.global_position)
	projectile.rotation = direction.angle()

	# Add projectile to the scene tree safely
	var current_scene = get_tree().current_scene
	if current_scene != null:
		current_scene.add_child(projectile)
	else:
		var root = get_tree().root
		if root != null:
			root.add_child(projectile)
		else:
			push_error("無法添加投射物：場景樹不可用")

## 獲取玩家位置
func _get_player_position() -> Vector2:
	# Try to find the player node
	var player = get_tree().get_first_node_in_group("player_group")
	if player != null and player is Node2D:
		return player.global_position
	
	# Fallback: try to find player by name
	var player_node = get_tree().get_node_or_null("Player")
	if player_node != null and player_node is Node2D:
		return player_node.global_position
	
	# If all else fails, return origin
	push_warning("無法找到玩家節點，使用原點位置")
	return Vector2.ZERO

func _find_nearest_enemy() -> Node2D:
	var enemies: Array = get_tree().get_nodes_in_group("enemy_group")
	if enemies.is_empty():
		return null
	
	var player_position: Vector2 = _get_player_position()
	var nearest_enemy: Node2D = null
	var shortest_distance: float = detection_range
	
	for enemy in enemies:
		if enemy is Node2D:
			var distance: float = player_position.distance_to(enemy.global_position)
			if distance < shortest_distance:
				shortest_distance = distance
				nearest_enemy = enemy
	
	return nearest_enemy

func _on_cooldown_timer_timeout() -> void:
	fire()
