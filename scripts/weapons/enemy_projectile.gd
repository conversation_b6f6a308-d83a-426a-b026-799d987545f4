extends Area2D
class_name EnemyProjectile

## 敵人投射物（支援節點池）
## 由射擊型敵人發射的攻擊投射物

@export var speed: float = 250.0
@export var damage: float = 20.0
@export var lifetime: float = 5.0
@export var knockback_force: float = 100.0

var direction: Vector2 = Vector2.ZERO
var velocity: Vector2 = Vector2.ZERO

func _ready() -> void:
	# 設置生命週期計時器
	var lifetime_timer = $LifetimeTimer
	if lifetime_timer != null:
		lifetime_timer.wait_time = lifetime
		if not lifetime_timer.timeout.is_connected(_on_lifetime_timer_timeout):
			lifetime_timer.timeout.connect(_on_lifetime_timer_timeout)
		lifetime_timer.start()

func _physics_process(delta: float) -> void:
	# 移動投射物
	global_position += velocity * delta

	# 旋轉投射物面向移動方向
	if velocity.length() > 0:
		rotation = velocity.angle()

func initialize(projectile_direction: Vector2, projectile_speed: float = -1.0) -> void:
	direction = projectile_direction.normalized()

	# 如果沒有指定速度，使用預設速度
	if projectile_speed > 0:
		speed = projectile_speed

	velocity = direction * speed

func set_projectile_properties(projectile_speed: float, projectile_damage: float) -> void:
	speed = projectile_speed
	damage = projectile_damage
	velocity = direction * speed

func _on_body_entered(body: Node2D) -> void:
	# 檢查是否擊中玩家
	if body.is_in_group("player_group"):
		_hit_player(body)

	# 檢查是否擊中牆壁或其他障礙物
	elif body.is_in_group("wall_group") or body.is_in_group("obstacle_group"):
		_hit_obstacle()

func _hit_player(player: Node2D) -> void:
	# 對玩家造成傷害
	var health_component = player.find_child("HealthComponent")
	if health_component != null and health_component.has_method("take_damage"):
		health_component.take_damage(damage)

	# 應用擊退效果
	if player.has_method("apply_knockback"):
		player.apply_knockback(direction * knockback_force)

	# 播放擊中特效
	var vfx_manager = get_node_or_null("/root/VFXManager")
	if vfx_manager:
		vfx_manager.play_hit_effect(global_position)

	# 播放命中音效
	_play_hit_sound()

	# 創建命中效果（保留原有的特定效果）
	_create_hit_effect()

	# 回收到節點池
	_return_to_pool()

func _hit_obstacle() -> void:
	# 播放擊中特效
	var vfx_manager = get_node_or_null("/root/VFXManager")
	if vfx_manager:
		vfx_manager.play_hit_effect(global_position)

	# 播放撞擊音效
	_play_hit_sound()

	# 創建撞擊效果（保留原有的特定效果）
	_create_hit_effect()

	# 回收到節點池
	_return_to_pool()

func _play_hit_sound() -> void:
	# 創建音效播放器
	var audio_player = AudioStreamPlayer.new()
	audio_player.volume_db = -15.0
	add_child(audio_player)

	# 播放音效（這裡可以添加具體的音效文件）
	# audio_player.stream = hit_sound
	audio_player.play()

	# 播放完成後移除
	audio_player.finished.connect(func(): audio_player.queue_free())

func _create_hit_effect() -> void:
	# 創建命中粒子效果
	var particles = GPUParticles2D.new()
	particles.emitting = true
	particles.amount = 10
	particles.lifetime = 0.5
	particles.explosiveness = 0.8
	# 建立並設定 Process Material（Godot 4）
	var particle_material = ParticleProcessMaterial.new()
	particle_material.initial_velocity_min = 50.0
	particle_material.initial_velocity_max = 100.0
	particle_material.gravity = Vector3(0, 0, 0)
	particle_material.color = Color(1, 0.2, 0.2, 1)
	particles.process_material = particle_material

	# 添加到場景
	var current_scene = get_tree().current_scene
	if current_scene != null:
		current_scene.add_child(particles)
		particles.global_position = global_position

		# 設置自動清理
		var cleanup_timer = Timer.new()
		cleanup_timer.wait_time = 1.0
		cleanup_timer.one_shot = true
		cleanup_timer.timeout.connect(func(): particles.queue_free())
		particles.add_child(cleanup_timer)
		cleanup_timer.start()

func _on_lifetime_timer_timeout() -> void:
	# 生命週期結束，回收到節點池
	_return_to_pool()

# 公共方法
func set_damage(new_damage: float) -> void:
	damage = new_damage

func set_speed(new_speed: float) -> void:
	speed = new_speed
	velocity = direction * speed

func set_knockback_force(new_force: float) -> void:
	knockback_force = new_force

# 節點池回調與回收
func on_taken_from_pool() -> void:
	# 重置狀態
	direction = direction.normalized()
	velocity = direction * speed
	visible = true
	set_deferred("monitoring", true)
	var lifetime_timer = $LifetimeTimer
	if lifetime_timer:
		lifetime_timer.stop()
		lifetime_timer.wait_time = lifetime
		# 使用 autostart 以確保入樹後自動啟動；若已在樹上則手動 start
		lifetime_timer.autostart = true
		if is_inside_tree():
			lifetime_timer.start()




func on_returned_to_pool() -> void:
	visible = false
	set_deferred("monitoring", false)

func _return_to_pool() -> void:
	var pool := get_tree().root.get_node_or_null("NodePoolManager")
	if pool:
		pool.return_node(self)
	else:
		queue_free()
