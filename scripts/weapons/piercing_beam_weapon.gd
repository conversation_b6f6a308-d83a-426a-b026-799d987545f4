extends Node2D
class_name PiercingBeamWeapon

@export var projectile_scene: PackedScene
@export var cooldown: float = 1.5
@export var projectile_damage: float = 15.0
@export var projectile_speed: float = 300.0
@export var max_pierces: int = 3

var player: Node
var is_active: bool = false

func _ready():
	# 連接計時器信號
	$CooldownTimer.timeout.connect(_on_cooldown_timer_timeout)
	
	# 獲取玩家引用 - 通過 WeaponManager 找到玩家
	player = _find_player()

func _find_player() -> Node:
	# 嘗試通過 WeaponManager 找到玩家
	var weapon_manager = get_parent()
	if weapon_manager and weapon_manager.get_parent():
		return weapon_manager.get_parent()
	
	# 如果找不到，返回 null
	print("警告：無法找到玩家引用")
	return null

func _process(_delta):
	if is_active and $CooldownTimer.is_stopped():
		shoot()

func activate():
	is_active = true
	$CooldownTimer.start()

func deactivate():
	is_active = false
	$CooldownTimer.stop()

func shoot():
	if not projectile_scene or not player:
		return
	
	# 從節點池取得投射物
	var projectile = NodePoolManager.acquire(projectile_scene)
	get_tree().current_scene.add_child(projectile)
	
	# 設置投射物位置和方向
	projectile.position = player.global_position
	
	# 獲取滑鼠方向（如果沒有滑鼠，使用玩家朝向）
	var direction: Vector2
	if Input.is_action_pressed("ui_accept"):  # 簡化，實際應該根據滑鼠位置
		direction = Vector2.RIGHT
	else:
		direction = Vector2.RIGHT
	
	projectile.set_direction(direction)
	
	# 設置投射物等級和屬性
	if projectile.has_method("set_level"):
		projectile.set_level(1)  # 這裡應該根據武器等級設置
	
	# 啟動冷卻計時器
	$CooldownTimer.start()

func _on_cooldown_timer_timeout():
	# 冷卻結束，可以再次發射
	pass

func set_level(level: int):
	# 根據等級調整屬性
	cooldown = 1.5 - (level - 1) * 0.1
	projectile_damage = 15.0 + (level - 1) * 5.0
	max_pierces = 3 + (level - 1)
	
	# 更新計時器
	$CooldownTimer.wait_time = cooldown
	
	# 更新投射物屬性
	if projectile_scene:
		var temp_projectile = projectile_scene.instantiate()
		if temp_projectile.has_method("set_level"):
			temp_projectile.set_level(level)
		temp_projectile.queue_free()
