extends Node2D
class_name ChargeTrailEffect

## 衝鋒拖尾效果
## 衝鋒型敵人在衝鋒時產生的拖尾效果

@export var trail_duration: float = 1.0
@export var fade_speed: float = 2.0

var _trail_particles: GPUParticles2D
var _trail_sprite: Sprite2D
var _lifetime_timer: Timer
var _fade_timer: float = 0.0

func _ready() -> void:
	# 獲取節點引用
	_trail_particles = $TrailParticles
	_trail_sprite = $TrailSprite
	_lifetime_timer = $LifetimeTimer
	
	# 設置生命週期
	_lifetime_timer.wait_time = trail_duration
	_lifetime_timer.start()
	
	# 開始拖尾效果
	_start_trail_effect()

func _process(delta: float) -> void:
	# 更新淡出效果
	_update_fade_effect(delta)

func _start_trail_effect() -> void:
	if _trail_particles == null:
		return
	
	# 開始發射粒子
	_trail_particles.emitting = true

func _update_fade_effect(delta: float) -> void:
	_fade_timer += delta
	
	# 計算透明度
	var alpha = 1.0 - (_fade_timer / trail_duration)
	alpha = clamp(alpha, 0.0, 1.0)
	
	# 應用透明度到精靈和粒子
	if _trail_sprite != null:
		_trail_sprite.modulate.a = alpha
	
	if _trail_particles != null:
		_trail_particles.modulate.a = alpha

func _on_lifetime_timer_timeout() -> void:
	# 生命週期結束，開始快速淡出
	_start_quick_fade()

func _start_quick_fade() -> void:
	# 創建快速淡出動畫
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	tween.tween_callback(queue_free)

# 公共方法
func set_trail_duration(duration: float) -> void:
	trail_duration = duration
	if _lifetime_timer != null:
		_lifetime_timer.wait_time = duration

func set_fade_speed(speed: float) -> void:
	fade_speed = speed

func set_trail_color(color: Color) -> void:
	if _trail_sprite != null:
		_trail_sprite.modulate = color
	if _trail_particles != null:
		_trail_particles.modulate = color
