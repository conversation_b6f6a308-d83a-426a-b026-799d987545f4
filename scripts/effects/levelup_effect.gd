extends Node2D
class_name LevelupEffect

## 升級特效
## 當玩家升級時產生的慶祝性光環效果

signal finished

@export var effect_duration: float = 3.0
@export var aura_particle_count: int = 80
@export var sparkle_particle_count: int = 40

var _aura_particles: GPUParticles2D
var _sparkle_particles: GPUParticles2D
var _timer: Timer

func _ready() -> void:
	# 獲取粒子節點
	_aura_particles = $AuraParticles
	_sparkle_particles = $SparkleParticles
	
	# 創建計時器
	_timer = Timer.new()
	_timer.wait_time = effect_duration
	_timer.one_shot = true
	_timer.timeout.connect(_on_timer_timeout)
	add_child(_timer)
	
	# 設置粒子參數
	if _aura_particles != null:
		_aura_particles.amount = aura_particle_count
	
	if _sparkle_particles != null:
		_sparkle_particles.amount = sparkle_particle_count

## 播放特效
func play() -> void:
	if _aura_particles != null:
		_aura_particles.emitting = true
		_aura_particles.restart()
	
	if _sparkle_particles != null:
		_sparkle_particles.emitting = true
		_sparkle_particles.restart()
	
	if _timer != null:
		_timer.start()

## 從節點池取出時的回調
func on_taken_from_pool() -> void:
	# 重置狀態
	if _aura_particles != null:
		_aura_particles.emitting = false
	
	if _sparkle_particles != null:
		_sparkle_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 返回節點池時的回調
func on_returned_to_pool() -> void:
	# 清理狀態
	if _aura_particles != null:
		_aura_particles.emitting = false
	
	if _sparkle_particles != null:
		_sparkle_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 計時器超時回調
func _on_timer_timeout() -> void:
	finished.emit()

## 設置特效顏色
func set_effect_color(color: Color) -> void:
	if _aura_particles != null:
		_aura_particles.modulate = color
	
	if _sparkle_particles != null:
		_sparkle_particles.modulate = color * Color(1.2, 1.2, 1.2, 0.8)

## 設置特效強度（粒子數量）
func set_effect_intensity(intensity: float) -> void:
	if _aura_particles != null:
		_aura_particles.amount = int(aura_particle_count * intensity)
	
	if _sparkle_particles != null:
		_sparkle_particles.amount = int(sparkle_particle_count * intensity)
