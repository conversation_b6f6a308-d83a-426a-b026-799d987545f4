extends Node2D
class_name HitEffect

## 擊中特效
## 當投射物擊中敵人時產生的火花效果

signal finished

@export var effect_duration: float = 0.8
@export var particle_count: int = 20

var _particles: GPUParticles2D
var _timer: Timer

func _ready() -> void:
	# 獲取粒子節點
	_particles = $HitParticles
	
	# 創建計時器
	_timer = Timer.new()
	_timer.wait_time = effect_duration
	_timer.one_shot = true
	_timer.timeout.connect(_on_timer_timeout)
	add_child(_timer)
	
	# 設置粒子參數
	if _particles != null:
		_particles.amount = particle_count
		_particles.lifetime = effect_duration

## 播放特效
func play() -> void:
	if _particles != null:
		_particles.emitting = true
		_particles.restart()
	
	if _timer != null:
		_timer.start()

## 從節點池取出時的回調
func on_taken_from_pool() -> void:
	# 重置狀態
	if _particles != null:
		_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 返回節點池時的回調
func on_returned_to_pool() -> void:
	# 清理狀態
	if _particles != null:
		_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 計時器超時回調
func _on_timer_timeout() -> void:
	finished.emit()

## 設置特效顏色
func set_effect_color(color: Color) -> void:
	if _particles != null:
		_particles.modulate = color

## 設置特效強度（粒子數量）
func set_effect_intensity(intensity: float) -> void:
	if _particles != null:
		_particles.amount = int(particle_count * intensity)
