extends Node2D
class_name ChargeImpactEffect

## 衝鋒撞擊效果
## 衝鋒型敵人撞擊目標或障礙物時產生的效果

@export var impact_duration: float = 1.0
@export var shake_intensity: float = 5.0
@export var shake_duration: float = 0.3

var _impact_particles: GPUParticles2D
var _impact_sprite: Sprite2D
var _lifetime_timer: Timer
var _shake_timer: float = 0.0
var _original_position: Vector2

func _ready() -> void:
	# 獲取節點引用
	_impact_particles = $ImpactParticles
	_impact_sprite = $ImpactSprite
	_lifetime_timer = $LifetimeTimer
	
	# 保存原始位置
	_original_position = global_position
	
	# 設置生命週期
	_lifetime_timer.wait_time = impact_duration
	_lifetime_timer.start()
	
	# 開始撞擊效果
	_start_impact_effect()
	
	# 播放撞擊音效
	_play_impact_sound()

func _process(delta: float) -> void:
	# 更新震動效果
	_update_shake_effect(delta)

func _start_impact_effect() -> void:
	if _impact_particles == null:
		return
	
	# 開始發射粒子
	_impact_particles.emitting = true

func _update_shake_effect(delta: float) -> void:
	_shake_timer += delta
	
	if _shake_timer < shake_duration:
		# 計算震動偏移
		var shake_offset = Vector2(
			randf_range(-shake_intensity, shake_intensity),
			randf_range(-shake_intensity, shake_intensity)
		)
		
		# 應用震動
		global_position = _original_position + shake_offset
	else:
		# 震動結束，恢復原始位置
		global_position = _original_position

func _play_impact_sound() -> void:
	# 創建音效播放器
	var audio_player = AudioStreamPlayer.new()
	audio_player.volume_db = -10.0
	add_child(audio_player)
	
	# 播放音效（這裡可以添加具體的音效文件）
	# audio_player.stream = impact_sound
	audio_player.play()
	
	# 播放完成後移除
	audio_player.finished.connect(func(): audio_player.queue_free())

func _on_lifetime_timer_timeout() -> void:
	# 生命週期結束，開始淡出效果
	_start_fade_out()

func _start_fade_out() -> void:
	# 創建淡出動畫
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	tween.tween_callback(queue_free)

# 公共方法
func set_impact_duration(duration: float) -> void:
	impact_duration = duration
	if _lifetime_timer != null:
		_lifetime_timer.wait_time = duration

func set_shake_intensity(intensity: float) -> void:
	shake_intensity = intensity

func set_shake_duration(duration: float) -> void:
	shake_duration = duration

func set_impact_color(color: Color) -> void:
	if _impact_sprite != null:
		_impact_sprite.modulate = color
	if _impact_particles != null:
		_impact_particles.modulate = color
