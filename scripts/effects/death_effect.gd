extends Node2D
class_name DeathEffect

## 死亡特效
## 當敵人死亡時產生的爆炸效果

signal finished

@export var effect_duration: float = 2.0
@export var explosion_particle_count: int = 50
@export var smoke_particle_count: int = 30

var _explosion_particles: GPUParticles2D
var _smoke_particles: GPUParticles2D
var _timer: Timer

func _ready() -> void:
	# 獲取粒子節點
	_explosion_particles = $ExplosionParticles
	_smoke_particles = $SmokeParticles
	
	# 創建計時器
	_timer = Timer.new()
	_timer.wait_time = effect_duration
	_timer.one_shot = true
	_timer.timeout.connect(_on_timer_timeout)
	add_child(_timer)
	
	# 設置粒子參數
	if _explosion_particles != null:
		_explosion_particles.amount = explosion_particle_count
	
	if _smoke_particles != null:
		_smoke_particles.amount = smoke_particle_count

## 播放特效
func play() -> void:
	if _explosion_particles != null:
		_explosion_particles.emitting = true
		_explosion_particles.restart()
	
	if _smoke_particles != null:
		_smoke_particles.emitting = true
		_smoke_particles.restart()
	
	if _timer != null:
		_timer.start()

## 從節點池取出時的回調
func on_taken_from_pool() -> void:
	# 重置狀態
	if _explosion_particles != null:
		_explosion_particles.emitting = false
	
	if _smoke_particles != null:
		_smoke_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 返回節點池時的回調
func on_returned_to_pool() -> void:
	# 清理狀態
	if _explosion_particles != null:
		_explosion_particles.emitting = false
	
	if _smoke_particles != null:
		_smoke_particles.emitting = false
	
	if _timer != null:
		_timer.stop()

## 計時器超時回調
func _on_timer_timeout() -> void:
	finished.emit()

## 設置特效顏色
func set_effect_color(color: Color) -> void:
	if _explosion_particles != null:
		_explosion_particles.modulate = color
	
	if _smoke_particles != null:
		_smoke_particles.modulate = color * Color(0.8, 0.8, 0.8, 0.7)

## 設置特效強度（粒子數量）
func set_effect_intensity(intensity: float) -> void:
	if _explosion_particles != null:
		_explosion_particles.amount = int(explosion_particle_count * intensity)
	
	if _smoke_particles != null:
		_smoke_particles.amount = int(smoke_particle_count * intensity)
