extends Node2D
class_name ChargeWarningEffect

## 衝鋒警告效果
## 在衝鋒型敵人衝鋒前顯示的警告效果

@export var warning_duration: float = 0.5
@export var pulse_speed: float = 3.0
@export var scale_range: Vector2 = Vector2(0.6, 1.2)

var _warning_sprite: Sprite2D
var _warning_particles: GPUParticles2D
var _lifetime_timer: Timer
var _pulse_timer: float = 0.0

func _ready() -> void:
	# 獲取節點引用
	_warning_sprite = $WarningSprite
	_warning_particles = $WarningParticles
	_lifetime_timer = $LifetimeTimer
	
	# 設置生命週期
	_lifetime_timer.wait_time = warning_duration
	_lifetime_timer.start()
	
	# 開始脈衝動畫
	_start_pulse_animation()
	
	# 開始粒子效果
	_start_particle_effect()

func _process(delta: float) -> void:
	# 更新脈衝動畫
	_update_pulse_animation(delta)

func _start_pulse_animation() -> void:
	if _warning_sprite == null:
		return
	
	# 設置初始縮放
	_warning_sprite.scale = Vector2.ONE

func _update_pulse_animation(delta: float) -> void:
	if _warning_sprite == null:
		return
	
	_pulse_timer += delta
	
	# 計算脈衝縮放
	var pulse_value = sin(_pulse_timer * pulse_speed)
	var scale_factor = lerp(scale_range.x, scale_range.y, (pulse_value + 1.0) * 0.5)
	
	_warning_sprite.scale = Vector2.ONE * scale_factor

func _start_particle_effect() -> void:
	if _warning_particles == null:
		return
	
	# 開始發射粒子
	_warning_particles.emitting = true

func _on_lifetime_timer_timeout() -> void:
	# 生命週期結束，開始淡出效果
	_start_fade_out()

func _start_fade_out() -> void:
	# 創建淡出動畫
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.2)
	tween.tween_callback(queue_free)

# 公共方法
func set_warning_duration(duration: float) -> void:
	warning_duration = duration
	if _lifetime_timer != null:
		_lifetime_timer.wait_time = duration

func set_pulse_speed(speed: float) -> void:
	pulse_speed = speed

func set_scale_range(min_scale: float, max_scale: float) -> void:
	scale_range = Vector2(min_scale, max_scale)
