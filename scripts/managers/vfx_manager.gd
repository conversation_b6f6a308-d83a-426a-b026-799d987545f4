extends Node

## VFX 管理器（建議設為 Autoload: 名稱 VFXManager）
## 負責管理和播放視覺特效，使用節點池來優化性能

# 預載入的特效場景
@export var hit_effect_scene: PackedScene
@export var death_effect_scene: PackedScene
@export var levelup_effect_scene: PackedScene

func _ready() -> void:
	# 預載入特效場景
	if hit_effect_scene == null:
		hit_effect_scene = preload("res://scenes/effects/hit_effect.tscn")
	if death_effect_scene == null:
		death_effect_scene = preload("res://scenes/effects/death_effect.tscn")
	if levelup_effect_scene == null:
		levelup_effect_scene = preload("res://scenes/effects/levelup_effect.tscn")

## 在指定位置播放視覺特效
## @param vfx_scene: 特效場景
## @param position: 播放位置
## @param parent: 父節點，如果為 null 則添加到場景樹根節點
func play_vfx(vfx_scene: PackedScene, position: Vector2, parent: Node = null) -> void:
	if vfx_scene == null:
		push_warning("VFXManager: 嘗試播放空的特效場景")
		return
	
	# 從節點池獲取特效實例
	var vfx_instance = NodePoolManager.acquire(vfx_scene)
	if vfx_instance == null:
		push_error("VFXManager: 無法獲取特效實例")
		return
	
	# 設置位置
	if vfx_instance is Node2D:
		vfx_instance.global_position = position
	elif vfx_instance is Control:
		vfx_instance.global_position = position
	
	# 添加到場景樹
	var target_parent = parent if parent != null else get_tree().current_scene
	if target_parent != null:
		target_parent.add_child(vfx_instance)
	else:
		push_error("VFXManager: 無法找到有效的父節點")
		NodePoolManager.return_node(vfx_instance)
		return
	
	# 如果特效有 finished 信號，連接到自動回收
	if vfx_instance.has_signal("finished"):
		vfx_instance.finished.connect(_on_vfx_finished.bind(vfx_instance), CONNECT_ONE_SHOT)
	
	# 如果特效有 play 方法，調用它
	if vfx_instance.has_method("play"):
		vfx_instance.play()

## 播放擊中特效
func play_hit_effect(position: Vector2, parent: Node = null) -> void:
	play_vfx(hit_effect_scene, position, parent)

## 播放死亡特效
func play_death_effect(position: Vector2, parent: Node = null) -> void:
	play_vfx(death_effect_scene, position, parent)

## 播放升級特效
func play_levelup_effect(position: Vector2, parent: Node = null) -> void:
	play_vfx(levelup_effect_scene, position, parent)

## 特效播放完成的回調
func _on_vfx_finished(vfx_instance: Node) -> void:
	if vfx_instance != null and is_instance_valid(vfx_instance):
		NodePoolManager.return_node(vfx_instance)
