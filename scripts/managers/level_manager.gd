extends Node

## LevelManager singleton for managing player experience and level progression.
## Handles experience accumulation, leveling up, and related game state.

signal level_up(new_level: int)
signal experience_updated(current_xp: int, required_xp: int)

## 升級選項池，包含所有可用的武器和技能數據
@export var upgrade_pool: Array[Resource] = []

var current_level: int = 1
var current_xp: int = 0

# Experience required for each level (index 0 = level 1, index 1 = level 2, etc.)
var xp_for_next_level: Array[int] = [10, 20, 30, 50, 80, 120, 170, 230, 300, 380, 470, 570, 680, 800, 930, 1070, 1220, 1380, 1550, 1730]

func _ready() -> void:
	# Ensure this is a singleton
	if get_tree().current_scene != self:
		experience_updated.emit(current_xp, _get_required_xp_for_current_level())
	
	# 總是初始化升級池，確保包含所有可用的升級選項
	_initialize_default_upgrade_pool()

func _initialize_default_upgrade_pool() -> void:
	"""初始化默認升級池"""
	print("正在初始化默認升級池...")
	
	# 載入所有武器資源
	var magic_missile = load("res://resources/weapons/magic_missile.tres")
	var guardian_shield = load("res://resources/weapons/guardian_shield.tres")
	var piercing_beam = load("res://resources/weapons/piercing_beam.tres")
	
	# 載入所有技能資源
	var agility_aura = load("res://resources/abilities/agility_aura.tres")
	var temporal_singularity = load("res://resources/abilities/temporal_singularity.tres")
	
	# 添加武器到升級池
	if magic_missile:
		upgrade_pool.append(magic_missile)
		print("已添加魔法飛彈到升級池")
	else:
		print("警告：無法載入魔法飛彈資源")
	
	if guardian_shield:
		upgrade_pool.append(guardian_shield)
		print("已添加守護者之盾到升級池")
	else:
		print("警告：無法載入守護者之盾資源")
	
	if piercing_beam:
		upgrade_pool.append(piercing_beam)
		print("已添加穿透光束到升級池")
	else:
		print("警告：無法載入穿透光束資源")
	
	# 添加技能到升級池
	if agility_aura:
		upgrade_pool.append(agility_aura)
		print("已添加敏捷光環到升級池")
	else:
		print("警告：無法載入敏捷光環資源")
	
	if temporal_singularity:
		upgrade_pool.append(temporal_singularity)
		print("已添加時間奇點到升級池")
	else:
		print("警告：無法載入時間奇點資源")
	
	print("升級池初始化完成，共 ", upgrade_pool.size(), " 個選項")

func add_experience(amount: int) -> void:
	current_xp += amount
	
	# Check if we can level up
	while current_xp >= _get_required_xp_for_current_level():
		_level_up()
	
	# Emit experience updated signal
	experience_updated.emit(current_xp, _get_required_xp_for_current_level())

func _level_up() -> void:
	var required_xp = _get_required_xp_for_current_level()
	current_xp -= required_xp
	current_level += 1
	
	# Emit level up signal
	level_up.emit(current_level)

func _get_required_xp_for_current_level() -> int:
	var level_index = current_level - 1
	if level_index >= xp_for_next_level.size():
		# If we've exceeded the predefined levels, use a formula
		return xp_for_next_level[-1] + (level_index - xp_for_next_level.size() + 1) * 100
	return xp_for_next_level[level_index]

func get_current_level() -> int:
	return current_level

func get_current_xp() -> int:
	return current_xp

func get_required_xp() -> int:
	return _get_required_xp_for_current_level()

func get_experience_percentage() -> float:
	var required = _get_required_xp_for_current_level()
	if required <= 0:
		return 1.0
	return float(current_xp) / float(required)

func reset_game_state() -> void:
	"""重置遊戲狀態，用於開始新遊戲時"""
	current_level = 1
	current_xp = 0
	
	# 清空並重新初始化升級池
	upgrade_pool.clear()
	_initialize_default_upgrade_pool()
	
	print("遊戲狀態已重置 - 等級: ", current_level, ", 經驗: ", current_xp)
	# 發出信號通知 UI 更新
	experience_updated.emit(current_xp, _get_required_xp_for_current_level())

## 從升級池中隨機選取指定數量的升級選項
func get_upgrade_options(count: int) -> Array:
	if upgrade_pool.is_empty():
		print("警告：升級池為空，無法提供升級選項")
		return []
	
	var available_options = upgrade_pool.duplicate()
	var selected_options: Array = []
	
	# 隨機選取指定數量的選項
	for i in range(min(count, available_options.size())):
		var random_index = randi() % available_options.size()
		selected_options.append(available_options[random_index])
		available_options.remove_at(random_index)
	
	print("已選取 ", selected_options.size(), " 個升級選項")
	return selected_options

## 獲取升級池中的所有選項
func get_all_upgrade_options() -> Array:
	return upgrade_pool.duplicate()

## 添加升級選項到池中
func add_upgrade_option(upgrade_data: Resource) -> void:
	if upgrade_data and not upgrade_pool.has(upgrade_data):
		upgrade_pool.append(upgrade_data)
		print("已添加升級選項：", upgrade_data.upgrade_name if upgrade_data.has_method("get") else "未知")

## 從升級池中移除升級選項
func remove_upgrade_option(upgrade_data: Resource) -> void:
	if upgrade_data and upgrade_pool.has(upgrade_data):
		upgrade_pool.erase(upgrade_data)
		print("已移除升級選項：", upgrade_data.upgrade_name if upgrade_data.has_method("get") else "未知")
