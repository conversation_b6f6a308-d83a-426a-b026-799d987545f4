extends Node

## 難度管理器
## 根據玩家等級和遊戲時間動態調整遊戲難度

signal difficulty_changed(new_difficulty: float)
signal boss_spawned(boss_instance: Node)

@export var base_difficulty: float = 1.0
@export var difficulty_scaling: float = 0.2  # 每級增加的難度倍數
@export var time_scaling: float = 0.1  # 每分鐘增加的難度倍數
@export var boss_spawn_time: float = 300.0 # 5 minutes
var boss_scene: PackedScene = preload("res://scenes/enemies/boss.tscn")

var current_difficulty: float = 1.0
var game_start_time: float = 0.0
var _level_manager: Node
var _boss_spawned: bool = false

func _ready() -> void:
	# 記錄遊戲開始時間
	game_start_time = Time.get_ticks_msec() / 1000.0
	
	# 尋找等級管理器
	_level_manager = get_tree().get_first_node_in_group("level_manager")
	
	# 連接等級變化信號
	if _level_manager != null and _level_manager.has_signal("level_up"):
		_level_manager.level_up.connect(_on_player_level_up)

func _process(_delta: float) -> void:
	# 根據遊戲時間更新難度
	_update_time_based_difficulty()
	_check_boss_spawn()

func _check_boss_spawn() -> void:
	if _boss_spawned or not boss_scene:
		return

	var time_elapsed = Time.get_ticks_msec() / 1000.0 - game_start_time
	if time_elapsed >= boss_spawn_time:
		_spawn_boss()

func _spawn_boss() -> void:
	_boss_spawned = true
	var boss_instance = boss_scene.instantiate()
	get_tree().get_root().add_child(boss_instance)
	boss_instance.global_position = get_tree().get_first_node_in_group("player_group").global_position + Vector2(0, -500)
	boss_spawned.emit(boss_instance)

func _update_time_based_difficulty() -> void:
	var current_time = Time.get_ticks_msec() / 1000.0
	var time_elapsed = current_time - game_start_time
	var time_bonus = (time_elapsed / 60.0) * time_scaling  # 每分鐘增加難度
	
	var new_difficulty = base_difficulty + time_bonus
	
	if abs(new_difficulty - current_difficulty) > 0.01:
		current_difficulty = new_difficulty
		difficulty_changed.emit(current_difficulty)

func _on_player_level_up(_new_level: int) -> void:
	# 根據玩家等級更新難度
	_update_level_based_difficulty()

func _update_level_based_difficulty() -> void:
	if _level_manager == null:
		return
	
	var player_level = _level_manager.get_current_level()
	var level_bonus = (player_level - 1) * difficulty_scaling
	
	var new_difficulty = base_difficulty + level_bonus
	
	if abs(new_difficulty - current_difficulty) > 0.01:
		current_difficulty = new_difficulty
		difficulty_changed.emit(current_difficulty)

# 公共方法
func get_current_difficulty() -> float:
	return current_difficulty

func get_difficulty_multiplier() -> float:
	return current_difficulty

func apply_difficulty_to_enemy_data(enemy_data: EnemyData) -> EnemyData:
	"""將難度倍數應用到敵人數據上"""
	if enemy_data == null:
		return enemy_data
	
	var modified_data = enemy_data.duplicate()
	var difficulty_mult = get_difficulty_multiplier()
	
	# 調整敵人屬性
	modified_data.max_health *= difficulty_mult
	modified_data.damage *= difficulty_mult
	modified_data.move_speed *= (1.0 + (difficulty_mult - 1.0) * 0.3)  # 速度增加較少
	modified_data.experience_value = int(enemy_data.experience_value * difficulty_mult)
	
	return modified_data

func get_spawn_interval_multiplier() -> float:
	"""獲取敵人生成間隔的倍數（難度越高，生成越快）"""
	return 1.0 / (1.0 + (current_difficulty - 1.0) * 0.5)

func get_max_enemies_multiplier() -> float:
	"""獲取最大敵人數量的倍數（難度越高，敵人越多）"""
	return 1.0 + (current_difficulty - 1.0) * 0.3

func reset_difficulty() -> void:
	"""重置難度（用於重新開始遊戲）"""
	current_difficulty = base_difficulty
	game_start_time = Time.get_ticks_msec() / 1000.0
	_boss_spawned = false
	difficulty_changed.emit(current_difficulty)
