extends Node

## 遊戲管理器單例，負責管理遊戲狀態和場景切換

signal scene_changed(new_scene_name: String)

var current_scene: Node
var _initialized: bool = false

func _ready() -> void:
	# 等待一幀確保場景樹完全初始化
	call_deferred("_initialize_game")

func _initialize_game() -> void:
	"""初始化遊戲，確保場景樹已經準備好"""
	if _initialized:
		return
	
	_initialized = true
	print("GameManager 初始化完成")

	# 處理初始場景
	var initial_scene = get_tree().root.get_child(get_tree().root.get_child_count() - 1)
	if initial_scene != null and not Engine.has_singleton(initial_scene.name):
		initial_scene.queue_free()
	
	# 在遊戲啟動時加載主選單，並播放主選單 BGM
	switch_scene("res://scenes/ui/main_menu.tscn")
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_bgm(am.menu_bgm)

func switch_scene(scene_path: String):
	if current_scene:
		current_scene.queue_free()

	var next_scene_res = load(scene_path)
	current_scene = next_scene_res.instantiate()
	get_tree().root.add_child(current_scene)

	# Apply permanent stats when entering the game world
	if scene_path.contains("game_world.tscn"):
		# Wait for the scene to be ready to ensure player exists
		await current_scene.ready
		var player = get_tree().get_first_node_in_group("player_group")
		if player and player.has_method("apply_permanent_upgrades"):
			player.apply_permanent_upgrades(SaveManager.stats)


func start_game() -> void:
	"""開始新遊戲"""
	# 重置遊戲狀態
	if LevelManager != null:
		LevelManager.reset_game_state()
	
	switch_scene("res://scenes/worlds/game_world.tscn")
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_bgm(am.battle_bgm)

func end_game() -> void:
	"""結束遊戲，顯示遊戲結束畫面"""
	# Save progress at the end of a run
	SaveManager.save_stats()
	
	if current_scene != null:
		# 創建遊戲結束畫面並疊加在當前場景上
		var game_over_scene = load("res://scenes/ui/game_over_screen.tscn")
		if game_over_scene != null:
			var game_over_screen = game_over_scene.instantiate()
			var root = get_tree().root
			if root != null:
				root.add_child(game_over_screen)
				# 暫停遊戲
				get_tree().paused = true
			else:
				push_error("場景樹根節點為 null，無法添加遊戲結束畫面")
		else:
			push_error("無法加載遊戲結束畫面場景")

func return_to_main_menu() -> void:
	"""返回主選單"""
	# 恢復遊戲暫停狀態
	get_tree().paused = false
	
	# 立即移除所有遊戲結束畫面
	call_deferred("_remove_game_over_screens_and_switch")

func _remove_game_over_screens_and_switch() -> void:
	"""移除遊戲結束畫面並切換到主選單"""
	var root = get_tree().root
	if root == null:
		push_error("場景樹根節點為 null，無法移除遊戲結束畫面")
		return
	
	# 查找並移除所有遊戲結束畫面
	for child in root.get_children():
		if child.name == "GameOverScreen":
			root.remove_child(child)
			child.queue_free()
			print("已移除遊戲結束畫面: ", child.name)
	
	# 切換到主選單，並播放主選單 BGM
	switch_scene("res://scenes/ui/main_menu.tscn")
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_bgm(am.menu_bgm)

func quit_game() -> void:
	"""退出遊戲"""
	get_tree().quit()
