extends CanvasLayer

## 輕量級性能監控疊加層（建議設為 Autoload）
## F3 切換顯示，顯示 FPS、process/physics 用時、draw calls。

var _label: Label
var _accum: float = 0.0
var _interval: float = 0.25
var _was_key_down := false

func _ready() -> void:
	# 建立 UI
	_label = Label.new()
	_label.theme_type_variation = "CodeLarge"
	_label.modulate = Color(1,1,1,0.85)
	_label.add_theme_font_size_override("font_size", 16)
	add_child(_label)
	_layer_to_top()
	set_process(true)
	visible = false

func _process(delta: float) -> void:
	# F3 切換顯示（緩觸發）
	var now_down := Input.is_key_pressed(KEY_F3)
	if now_down and not _was_key_down:
		visible = not visible
	_was_key_down = now_down
	if not visible:
		return
	_accum += delta
	if _accum >= _interval:
		_accum = 0.0
		_update_text()

func _update_text() -> void:
	var fps: int = Engine.get_frames_per_second()
	var p_time := float(Performance.get_monitor(Performance.Monitor.TIME_PROCESS))
	var phy_time := float(Performance.get_monitor(Performance.Monitor.TIME_PHYSICS_PROCESS))
	var draw_calls := int(Performance.get_monitor(Performance.Monitor.RENDER_TOTAL_DRAW_CALLS_IN_FRAME))
	_label.text = "FPS: %d\nprocess: %.3f ms\nphysics: %.3f ms\ndraw_calls: %d" % [fps, p_time*1000.0, phy_time*1000.0, draw_calls]
	_label.position = Vector2(10, 10)

func _layer_to_top() -> void:
	# 確保在最上層
	layer = 128

