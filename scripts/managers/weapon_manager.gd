extends Node

## 武器管理器，負責管理玩家的所有武器
## 處理武器的添加、升級和實例化

## 擁有的武器字典，key 是 weapon_data.id，value 是武器場景實例
var owned_weapons: Dictionary = {}

## 武器等級追蹤，key 是 weapon_data.id，value 是當前等級 (從 1 開始)
var weapon_levels: Dictionary = {}

## 添加新武器
func add_weapon(weapon_data: WeaponData) -> bool:
	if not weapon_data:
		print("錯誤：無效的武器數據")
		return false
	
	# 獲取武器場景
	var weapon_scene: PackedScene = null
	if weapon_data.weapon_scene:
		weapon_scene = weapon_data.weapon_scene
	elif weapon_data.scene_path:
		weapon_scene = load(weapon_data.scene_path)
		if not weapon_scene:
			print("錯誤：無法載入場景路徑：", weapon_data.scene_path)
			return false
	else:
		print("錯誤：武器數據中沒有場景引用或路徑")
		return false
	
	# 檢查是否已經擁有此武器
	if owned_weapons.has(weapon_data.id):
		# 如果已擁有，則升級現有武器
		return upgrade_weapon(weapon_data)
	
	# 實例化新武器
	var weapon_instance = weapon_scene.instantiate()
	if not weapon_instance:
		print("錯誤：無法實例化武器場景")
		return false
	
	# 設置武器屬性
	_apply_weapon_stats(weapon_instance, weapon_data, 1)
	
	# 添加到場景樹和追蹤字典
	add_child(weapon_instance)
	owned_weapons[weapon_data.id] = weapon_instance
	weapon_levels[weapon_data.id] = 1
	
	print("成功添加武器：", weapon_data.upgrade_name)
	return true

## 升級現有武器
func upgrade_weapon(weapon_data: WeaponData) -> bool:
	if not weapon_data or not owned_weapons.has(weapon_data.id):
		print("錯誤：無法升級不存在的武器")
		return false
	
	var current_level = weapon_levels[weapon_data.id]
	var next_level = current_level + 1
	
	# 檢查是否已達到最大等級
	if weapon_data.is_max_level(current_level):
		print("武器已達到最大等級：", weapon_data.upgrade_name)
		return false
	
	# 獲取下一級的統計數據
	var next_level_stats = weapon_data.get_stats_for_level(next_level)
	if next_level_stats.is_empty():
		print("錯誤：無法獲取下一級統計數據")
		return false
	
	# 應用新的統計數據
	var weapon_instance = owned_weapons[weapon_data.id]
	_apply_weapon_stats(weapon_instance, weapon_data, next_level)
	
	# 更新等級
	weapon_levels[weapon_data.id] = next_level
	
	print("成功升級武器：", weapon_data.upgrade_name, " 到等級 ", next_level)
	return true

## 獲取武器的當前等級
func get_weapon_level(weapon_id: String) -> int:
	return weapon_levels.get(weapon_id, 0)

## 檢查是否擁有指定武器
func has_weapon(weapon_id: String) -> bool:
	return owned_weapons.has(weapon_id)

## 獲取所有擁有的武器
func get_owned_weapons() -> Array:
	return owned_weapons.values()

## 獲取武器數量
func get_weapon_count() -> int:
	return owned_weapons.size()

## 應用武器統計數據到武器實例
func _apply_weapon_stats(weapon_instance: Node, weapon_data: WeaponData, level: int):
	var stats = weapon_data.get_stats_for_level(level)
	
	# 遍歷統計數據並應用到武器實例
	for stat_name in stats.keys():
		var stat_value = stats[stat_name]
		
		# 嘗試設置屬性
		if weapon_instance.has_method("set_" + stat_name):
			weapon_instance.call("set_" + stat_name, stat_value)
		elif _has_property(weapon_instance, stat_name):
			weapon_instance.set(stat_name, stat_value)
		else:
			# 如果無法直接設置，嘗試通過信號通知
			if weapon_instance.has_signal("stat_changed"):
				weapon_instance.emit_signal("stat_changed", stat_name, stat_value)

## 檢查節點是否具有指定屬性
func _has_property(node: Node, property_name: String) -> bool:
	# Try to get the property value to check if it exists
	# If the property doesn't exist, get() will return null
	var test_value = node.get(property_name)
	return test_value != null

## 移除武器
func remove_weapon(weapon_id: String) -> bool:
	if not owned_weapons.has(weapon_id):
		return false
	
	var weapon_instance = owned_weapons[weapon_id]
	weapon_instance.queue_free()
	
	owned_weapons.erase(weapon_id)
	weapon_levels.erase(weapon_id)
	
	print("成功移除武器：", weapon_id)
	return true

## 清空所有武器
func clear_all_weapons():
	for weapon_id in owned_weapons.keys():
		remove_weapon(weapon_id)
