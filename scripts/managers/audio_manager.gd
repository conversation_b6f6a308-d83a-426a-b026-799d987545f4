extends Node

# 集中式音訊管理器（Autoload 單例）
# - 提供 SFX 與 BGM 播放
# - 以 Audio Bus 管控音量（預期存在 "SFX" 與 "BGM" 兩條匯流排）

const SFX_BUS_NAME := "SFX"
const BGM_BUS_NAME := "BGM"

@export var menu_bgm: AudioStream
@export var battle_bgm: AudioStream
@export var ui_click_sfx: AudioStream
@export var weapon_fire_sfx: AudioStream
@export var hit_sfx: AudioStream
@export var enemy_death_sfx: AudioStream

var sfx_player: AudioStreamPlayer
var bgm_player: AudioStreamPlayer

func _ready() -> void:
	# 建立子播放器並設定到對應 Bus
	sfx_player = AudioStreamPlayer.new()
	bgm_player = AudioStreamPlayer.new()

	# 指定輸出匯流排（若不存在，仍會走 Master，但我們會提出警告）
	sfx_player.bus = SFX_BUS_NAME
	bgm_player.bus = BGM_BUS_NAME

	add_child(sfx_player)
	add_child(bgm_player)

	# 確保 BGM 會循環播放
	if not bgm_player.finished.is_connected(_on_bgm_finished):
		bgm_player.finished.connect(_on_bgm_finished)

	# 開機時不要殘留音量或狀態
	sfx_player.stop()
	bgm_player.stop()

func play_sfx(sfx_resource: AudioStream, volume_db: float = 0.0) -> void:
	if sfx_resource == null:
		return
	# 盡量避免搶斷正在播放的短 SFX：用一個臨時的播放器（走同一個 SFX bus）
	var p := AudioStreamPlayer.new()
	p.bus = SFX_BUS_NAME
	p.stream = sfx_resource
	p.volume_db = volume_db
	add_child(p)
	p.play()
	p.finished.connect(func(): p.queue_free())

func play_ui_click() -> void:
	if ui_click_sfx:
		play_sfx(ui_click_sfx)

func play_bgm(bgm_resource: AudioStream) -> void:
	if bgm_resource == null:
		# 沒有資源就靜音
		bgm_player.stop()
		bgm_player.stream = null
		return
	var changed := bgm_player.stream != bgm_resource
	bgm_player.stream = bgm_resource
	if changed:
		# 重接一次，避免多重連線
		if bgm_player.finished.is_connected(_on_bgm_finished):
			bgm_player.finished.disconnect(_on_bgm_finished)
		bgm_player.finished.connect(_on_bgm_finished)
	bgm_player.play()

func _on_bgm_finished() -> void:
	# 以最保守的方式循環
	if bgm_player.stream != null:
		bgm_player.play()

# 音量（dB）控制，實作為設定 Audio Bus 音量
func set_sfx_volume(volume_db: float) -> void:
	var idx := _get_bus_index(SFX_BUS_NAME)
	if idx >= 0:
		AudioServer.set_bus_volume_db(idx, volume_db)
	else:
		push_warning("找不到音訊匯流排 '" + SFX_BUS_NAME + "'，請在 Audio Bus Layout 中建立。")

func set_bgm_volume(volume_db: float) -> void:
	var idx := _get_bus_index(BGM_BUS_NAME)
	if idx >= 0:
		AudioServer.set_bus_volume_db(idx, volume_db)
	else:
		push_warning("找不到音訊匯流排 '" + BGM_BUS_NAME + "'，請在 Audio Bus Layout 中建立。")

func get_sfx_volume_db() -> float:
	var idx := _get_bus_index(SFX_BUS_NAME)
	return AudioServer.get_bus_volume_db(idx) if idx >= 0 else 0.0

func get_bgm_volume_db() -> float:
	var idx := _get_bus_index(BGM_BUS_NAME)
	return AudioServer.get_bus_volume_db(idx) if idx >= 0 else 0.0

func _get_bus_index(bus_name: String) -> int:
	for i in range(AudioServer.get_bus_count()):
		if AudioServer.get_bus_name(i) == bus_name:
			return i
	return -1

