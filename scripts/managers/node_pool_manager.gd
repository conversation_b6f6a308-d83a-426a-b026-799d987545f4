extends Node

## 節點複用池管理器（建議設為 Autoload: 名稱 NodePoolManager）
## - get_node(scene): 取得一個指定場景的可用節點（先取池，沒有則 instantiate）
## - return_node(node): 歸還節點到池中（停用處理、隱藏、脫離父節點）
## 可選介面：節點若實作 on_taken_from_pool() / on_returned_to_pool() 會在取用/歸還時被呼叫。

var _pools: Dictionary = {} # key: String(scene_path) -> Array[Node]
const META_SCENE_PATH := "pooled_scene_path"

func _get_key_for_scene(scene: PackedScene) -> String:
	return scene.resource_path if scene and scene.resource_path != "" else "__dynamic__"

func _get_key_for_node(node: Node) -> String:
	if node.has_meta(META_SCENE_PATH):
		return str(node.get_meta(META_SCENE_PATH))
	# Godot 4 提供 scene_file_path，可作為後備
	if node.has_method("get_scene_file_path"):
		var p = node.get_scene_file_path()
		if typeof(p) == TYPE_STRING and p != "":
			return p
	return "__dynamic__"

func acquire(scene: PackedScene) -> Node:
	var key := _get_key_for_scene(scene)
	var pool: Array = _pools.get(key, [])
	var node: Node = null
	# 從池中取出有效節點
	while pool.size() > 0 and node == null:
		var cand = pool.pop_back()
		if cand != null and is_instance_valid(cand):
			node = cand
	# 若池為空或皆為無效，instantiate
	if node == null:
		node = scene.instantiate()
		# 標記來源，便於歸還
		node.set_meta(META_SCENE_PATH, key)
	# 重新啟用
	if node is CanvasItem:
		node.visible = true
	# 恢復處理
	node.set_process(true)
	node.set_physics_process(true)
	if node.has_method("set_process_input"):
		node.set_process_input(true)
	if node.has_method("set_process_unhandled_input"):
		node.set_process_unhandled_input(true)
	# 回調：節點自行復位
	if node.has_method("on_taken_from_pool"):
		node.on_taken_from_pool()
	return node

func return_node(node: Node) -> void:
	# 在物理回呼期間移除節點會報錯，統一延後處理
	if node == null or not is_instance_valid(node) or node.is_queued_for_deletion():
		return
	call_deferred("_deferred_return_node", node)

func _deferred_return_node(node: Node) -> void:
	if node == null or not is_instance_valid(node) or node.is_queued_for_deletion():
		return
	# 回調：節點自行清理狀態
	if node.has_method("on_returned_to_pool"):
		node.on_returned_to_pool()
	# 從場景樹脫離並停用
	if node.get_parent():
		node.get_parent().remove_child(node)
	if node is CanvasItem:
		node.visible = false
	node.set_process(false)
	node.set_physics_process(false)
	if node.has_method("set_process_input"):
		node.set_process_input(false)
	if node.has_method("set_process_unhandled_input"):
		node.set_process_unhandled_input(false)
	# 放回池（避免重複放入）
	var key := _get_key_for_node(node)
	if not _pools.has(key):
		_pools[key] = []
	var pool: Array = _pools[key]
	if not pool.has(node):
		pool.append(node)

func clear_pool(scene: PackedScene = null) -> void:
	# 僅釋放池內物件，不影響當前場景中的使用中節點
	if scene == null:
		_pools.clear()
		return
	var key := _get_key_for_scene(scene)
	_pools.erase(key)
