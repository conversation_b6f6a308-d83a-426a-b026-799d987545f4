extends Node

const SAVE_PATH = "user://permanent_stats.tres"

var stats: PermanentStats


func _ready():
	load_or_create_stats()


func load_or_create_stats():
	if FileAccess.file_exists(SAVE_PATH):
		stats = ResourceLoader.load(SAVE_PATH, "PermanentStats", ResourceLoader.CACHE_MODE_REUSE)
		print("Permanent stats loaded from %s." % SAVE_PATH)
	else:
		stats = PermanentStats.new()
		print("No save file found. Creating new permanent stats.")
		save_stats()


func save_stats():
	var error = ResourceSaver.save(stats, SAVE_PATH)
	if error != OK:
		printerr("Failed to save permanent stats! Error code: %s" % error)
	else:
		print("Permanent stats saved successfully to %s." % SAVE_PATH)


func add_currency(amount: int):
	stats.meta_currency += amount
	# Note: We don't save immediately here.
	# Saving should happen at defined points, like the end of a run.

