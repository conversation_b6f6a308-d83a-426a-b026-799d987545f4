extends Node

## A reusable health component for any character.
## Handles health management, damage taking, and death signaling.

signal died

@export var max_health: float = 10.0

var current_health: float

func _ready() -> void:
	current_health = max_health

func take_damage(amount: float) -> void:
	current_health -= amount
	current_health = maxf(current_health, 0.0)  # Clamp to 0 minimum
	
	if current_health <= 0.0:
		died.emit()

func heal(amount: float) -> void:
	current_health += amount
	current_health = minf(current_health, max_health)  # Clamp to max_health

func get_health_percentage() -> float:
	return current_health / max_health if max_health > 0.0 else 0.0
