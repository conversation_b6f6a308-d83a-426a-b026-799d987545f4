extends Node
class_name <PERSON><PERSON>ealthComponent

## A health component for bosses with multiple health stages.

signal stage_cleared(stage_index)
signal boss_defeated

@export var health_stages: Array[float] = [1000.0, 1000.0, 1000.0]

var current_stage: int = 0
var current_health: float

func _ready() -> void:
	if health_stages.is_empty():
		push_error("Boss health stages are not configured!")
		return
	current_health = health_stages[0]

func take_damage(amount: float) -> void:
	if current_stage >= health_stages.size():
		return # Already defeated

	current_health -= amount
	current_health = maxf(current_health, 0.0)

	if current_health <= 0.0:
		stage_cleared.emit(current_stage)
		current_stage += 1
		
		if current_stage >= health_stages.size():
			boss_defeated.emit()
		else:
			# Move to the next stage
			current_health = health_stages[current_stage]

func get_current_stage_health_percentage() -> float:
	if current_stage >= health_stages.size():
		return 0.0
	return current_health / health_stages[current_stage] if health_stages[current_stage] > 0.0 else 0.0

func get_total_health_percentage() -> float:
	var total_health = 0.0
	for health in health_stages:
		total_health += health
	
	if total_health == 0.0:
		return 0.0

	var remaining_health = 0.0
	for i in range(current_stage, health_stages.size()):
		if i == current_stage:
			remaining_health += current_health
		else:
			remaining_health += health_stages[i]
	
	return remaining_health / total_health
