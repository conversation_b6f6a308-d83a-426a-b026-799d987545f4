extends Control

## A visual health bar that displays current health using ColorRect nodes.
## Automatically updates when connected to a HealthComponent.

@export var bar_width: float = 40.0
@export var bar_height: float = 6.0
@export var background_color: Color = Color(0.2, 0.2, 0.2, 0.8)
@export var health_color: Color = Color(0.8, 0.2, 0.2, 1.0)
@export var border_color: Color = Color.WHITE

var _health_component: Node
var _background: ColorRect
var _health_fill: ColorRect
var _max_health: float
var _current_health: float

func _ready() -> void:
	# Set the minimum size for this control node so the container can arrange it.
	custom_minimum_size = Vector2(bar_width, bar_height)

	# Create background
	_background = ColorRect.new()
	_background.name = "Background"
	_background.size = Vector2(bar_width, bar_height)
	_background.color = background_color
	add_child(_background)
	
	# Create health fill
	_health_fill = ColorRect.new()
	_health_fill.name = "HealthFill"
	_health_fill.size = Vector2(bar_width, bar_height)
	_health_fill.color = health_color
	add_child(_health_fill)

func connect_to_health_component(health_component: Node) -> void:
	if health_component == null:
		return
		
	_health_component = health_component
	_max_health = health_component.max_health
	_current_health = health_component.current_health
	_update_health_bar()

func _process(_delta: float) -> void:
	if _health_component != null:
		_current_health = _health_component.current_health
		_update_health_bar()
		
		# Show health bar only when damaged
		visible = _current_health < _max_health

func _update_health_bar() -> void:
	if _max_health <= 0:
		return
		
	var health_percentage: float = _current_health / _max_health
	var fill_width: float = bar_width * health_percentage
	_health_fill.size.x = fill_width
