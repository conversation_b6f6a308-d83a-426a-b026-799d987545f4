extends Node

## 測試腳本：驗證 TASK008 中創建的資源文件

func _ready():
	print("=== 開始測試 TASK008 資源文件 ===")
	
	# 測試武器資源
	test_weapon_resources()
	
	# 測試技能資源
	test_ability_resources()
	
	print("=== 測試完成 ===")

func test_weapon_resources():
	print("\n--- 測試武器資源 ---")
	
	var weapons = [
		"res://resources/weapons/magic_missile.tres",
		"res://resources/weapons/guardian_shield.tres",
		"res://resources/weapons/piercing_beam.tres"
	]
	
	for weapon_path in weapons:
		var weapon = load(weapon_path)
		if weapon:
			print("✅ 成功載入: ", weapon_path)
			print("  - 類型: ", weapon.get_class())
			print("  - 名稱: ", weapon.upgrade_name)
			print("  - 描述: ", weapon.description)
		else:
			print("❌ 無法載入: ", weapon_path)

func test_ability_resources():
	print("\n--- 測試技能資源 ---")
	
	var abilities = [
		"res://resources/abilities/agility_aura.tres",
		"res://resources/abilities/temporal_singularity.tres"
	]
	
	for ability_path in abilities:
		var ability = load(ability_path)
		if ability:
			print("✅ 成功載入: ", ability_path)
			print("  - 類型: ", ability.get_class())
			print("  - 名稱: ", ability.upgrade_name)
			print("  - 描述: ", ability.description)
		else:
			print("❌ 無法載入: ", ability_path)
