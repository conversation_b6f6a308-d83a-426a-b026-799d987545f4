extends Resource
class_name PermanentStats

## The currency earned during runs to purchase permanent upgrades.
@export var meta_currency: int = 0

@export_group("Permanent Stat Bonuses")
## Flat bonus added to the player's max health at the start of a run.
@export var bonus_health: int = 0
## Flat bonus added to the player's move speed at the start of a run.
@export var bonus_move_speed: float = 0.0
## Percentage bonus added to the player's damage multiplier.
@export var bonus_damage_multiplier: float = 0.0
## Percentage bonus that reduces weapon cooldowns.
@export var bonus_cooldown_multiplier: float = 0.0
