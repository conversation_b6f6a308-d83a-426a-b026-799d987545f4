class_name WeaponData
extends UpgradeData

## 武器數據資源，繼承自 UpgradeData
## 包含武器特有的屬性，如場景文件和等級統計數據

@export var weapon_scene: PackedScene
@export var scene_path: String = ""
@export var base_damage: float = 10.0
@export var base_cooldown: float = 1.0
@export var base_range: float = 100.0
@export var stats_per_level: Dictionary = {}

## 獲取指定等級的武器統計數據
func get_stats_for_level(level: int) -> Dictionary:
	var level_str = str(level)
	if not stats_per_level.has(level_str):
		return {}
	
	return stats_per_level[level_str]

## 獲取最大等級
func get_max_level() -> int:
	return stats_per_level.size()

## 檢查是否已達到最大等級
func is_max_level(current_level: int) -> bool:
	return current_level >= get_max_level()
