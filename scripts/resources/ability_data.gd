class_name AbilityData
extends UpgradeData

## 技能數據資源，繼承自 UpgradeData
## 包含技能特有的屬性，如屬性加成和效果

@export var stat_bonuses: Dictionary = {}
@export var max_stacks: int = 1
@export var current_stacks: int = 0

## 獲取當前堆疊數的屬性加成
func get_current_stat_bonuses() -> Dictionary:
	var bonuses = {}
	for stat_name in stat_bonuses.keys():
		bonuses[stat_name] = stat_bonuses[stat_name] * current_stacks
	return bonuses

## 增加技能堆疊數
func add_stack() -> bool:
	if current_stacks < max_stacks:
		current_stacks += 1
		return true
	return false

## 檢查是否可以增加堆疊
func can_add_stack() -> bool:
	return current_stacks < max_stacks

## 重置堆疊數
func reset_stacks():
	current_stacks = 0
