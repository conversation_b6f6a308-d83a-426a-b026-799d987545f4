## 測試腳本，用於驗證資源類別的正確定義
## 此腳本僅用於開發階段驗證，可以在完成測試後刪除

extends Node

func _ready():
	print("開始測試資源類別...")
	test_upgrade_data()
	test_weapon_data()
	test_ability_data()
	print("資源類別測試完成！")

func test_upgrade_data():
	print("測試 UpgradeData...")
	var upgrade = UpgradeData.new()
	upgrade.id = "test_upgrade"
	upgrade.upgrade_name = "測試升級"
	upgrade.description = "這是一個測試升級"
	
	assert(upgrade.id == "test_upgrade")
	assert(upgrade.upgrade_name == "測試升級")
	assert(upgrade.description == "這是一個測試升級")
	print("✅ UpgradeData 測試通過")

func test_weapon_data():
	print("測試 WeaponData...")
	var weapon = WeaponData.new()
	weapon.id = "test_weapon"
	weapon.upgrade_name = "測試武器"
	weapon.description = "這是一個測試武器"
	
	# 添加等級統計數據
	weapon.stats_per_level = [
		{"damage": 10, "cooldown": 1.0},
		{"damage": 15, "cooldown": 0.8}
	]
	
	assert(weapon.get_max_level() == 2)
	assert(weapon.get_stats_for_level(1)["damage"] == 10)
	assert(weapon.get_stats_for_level(2)["damage"] == 15)
	assert(not weapon.is_max_level(1))
	assert(weapon.is_max_level(2))
	print("✅ WeaponData 測試通過")

func test_ability_data():
	print("測試 AbilityData...")
	var ability = AbilityData.new()
	ability.id = "test_ability"
	ability.upgrade_name = "測試技能"
	ability.description = "這是一個測試技能"
	ability.stat_bonuses = {"move_speed_multiplier": 1.2, "damage_multiplier": 1.1}
	ability.max_stacks = 3
	
	assert(ability.can_add_stack())
	assert(ability.add_stack())
	assert(ability.current_stacks == 1)
	assert(ability.get_current_stat_bonuses()["move_speed_multiplier"] == 1.2)
	
	ability.add_stack()
	ability.add_stack()
	assert(ability.current_stacks == 3)
	assert(not ability.can_add_stack())
	
	ability.reset_stacks()
	assert(ability.current_stacks == 0)
	print("✅ AbilityData 測試通過")
