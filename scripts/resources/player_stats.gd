class_name PlayerStats
extends Resource

## 玩家統計數據資源，集中管理所有可變屬性
## 讓外部系統（如技能）可以安全地修改玩家狀態

@export var max_health: float = 100.0
@export var move_speed: float = 300.0
@export var damage_multiplier: float = 1.0
@export var cooldown_multiplier: float = 1.0
@export var attack_speed_multiplier: float = 1.0
@export var projectile_speed_multiplier: float = 1.0
@export var pickup_range: float = 100.0
@export var experience_multiplier: float = 1.0

## 應用屬性加成
func apply_stat_bonus(stat_name: String, bonus_value: float, is_multiplier: bool = false):
	# Check if the property exists by trying to get it
	if not _has_property(stat_name):
		return
	
	var current_value = get(stat_name)
	if is_multiplier:
		set(stat_name, current_value * bonus_value)
	else:
		set(stat_name, current_value + bonus_value)

## 檢查屬性是否存在
func _has_property(stat_name: String) -> bool:
	# List of valid properties
	var valid_properties = [
		"max_health", "move_speed", "damage_multiplier", 
		"cooldown_multiplier", "attack_speed_multiplier", 
		"projectile_speed_multiplier", "pickup_range", "experience_multiplier"
	]
	return stat_name in valid_properties

## 重置所有屬性到預設值
func reset_to_defaults():
	max_health = 100.0
	move_speed = 300.0
	damage_multiplier = 1.0
	cooldown_multiplier = 1.0
	attack_speed_multiplier = 1.0
	projectile_speed_multiplier = 1.0
	pickup_range = 100.0
	experience_multiplier = 1.0
