extends Resource
class_name EnemyData

## 敵人類型枚舉
enum EnemyType {
	TRACKER,    # 追蹤型敵人
	SHOOTER,    # 射擊型敵人
	CHARGER,    # 衝鋒型敵人
	BOSS        # 頭目
}

## 基礎敵人屬性
@export var enemy_type: EnemyType
@export var max_health: float = 100.0
@export var move_speed: float = 150.0
@export var damage: float = 10.0
@export var damage_cooldown: float = 1.0
@export var experience_value: int = 10
@export var detection_range: float = 300.0  # 偵測範圍
@export var attack_range: float = 50.0      # 攻擊範圍

## 視覺資源
@export var sprite_texture: Texture2D
@export var particle_effect: PackedScene

## 音效資源
@export var hurt_sound: AudioStream
@export var death_sound: AudioStream
@export var attack_sound: AudioStream
@export var move_sound: AudioStream

## 根據類型獲取預設值
static func get_default_data(type: EnemyType) -> EnemyData:
	var data = EnemyData.new()
	data.enemy_type = type
	
	match type:
		EnemyType.TRACKER:
			data.max_health = 80.0
			data.move_speed = 180.0
			data.damage = 15.0
			data.detection_range = 350.0
			data.attack_range = 40.0
			data.experience_value = 12
			
		EnemyType.SHOOTER:
			data.max_health = 60.0
			data.move_speed = 100.0
			data.damage = 20.0
			data.detection_range = 400.0
			data.attack_range = 200.0
			data.experience_value = 15
			
		EnemyType.CHARGER:
			data.max_health = 120.0
			data.move_speed = 250.0
			data.damage = 25.0
			data.detection_range = 300.0
			data.attack_range = 60.0
			data.experience_value = 18
		
		EnemyType.BOSS:
			data.max_health = 2000.0
			data.move_speed = 100.0
			data.damage = 50.0
			data.detection_range = 1000.0
			data.attack_range = 500.0
			data.experience_value = 500
	
	return data
