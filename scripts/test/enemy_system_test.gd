extends Node
class_name EnemySystemTest

## 敵人系統測試腳本
## 用於測試和驗證新的敵人AI系統

@export var test_enemy_scene: PackedScene
@export var test_player_scene: PackedScene

var _test_enemies: Array[Node2D] = []
var _test_player: Node2D
var _test_world: Node2D

func _ready() -> void:
	print("=== 敵人系統測試開始 ===")
	
	# 延遲執行測試，確保場景完全載入
	call_deferred("_run_tests")

func _run_tests() -> void:
	# 測試1: 創建敵人數據資源
	_test_enemy_data_creation()
	
	# 測試2: 測試AI狀態機
	_test_ai_state_machine()
	
	# 測試3: 測試敵人類型系統
	_test_enemy_type_system()
	
	# 測試4: 測試投射物系統
	_test_projectile_system()
	
	print("=== 敵人系統測試完成 ===")

func _test_enemy_data_creation() -> void:
	print("測試1: 敵人數據資源創建")
	
	# 測試追蹤型敵人數據
	var tracker_data = EnemyData.get_default_data(EnemyData.EnemyType.TRACKER)
	assert(tracker_data != null, "追蹤型敵人數據創建失敗")
	assert(tracker_data.enemy_type == EnemyData.EnemyType.TRACKER, "敵人類型設置錯誤")
	assert(tracker_data.max_health == 80.0, "生命值設置錯誤")
	assert(tracker_data.move_speed == 180.0, "移動速度設置錯誤")
	
	# 測試射擊型敵人數據
	var shooter_data = EnemyData.get_default_data(EnemyData.EnemyType.SHOOTER)
	assert(shooter_data != null, "射擊型敵人數據創建失敗")
	assert(shooter_data.enemy_type == EnemyData.EnemyType.SHOOTER, "敵人類型設置錯誤")
	assert(shooter_data.attack_range == 200.0, "攻擊範圍設置錯誤")
	
	# 測試衝鋒型敵人數據
	var charger_data = EnemyData.get_default_data(EnemyData.EnemyType.CHARGER)
	assert(charger_data != null, "衝鋒型敵人數據創建失敗")
	assert(charger_data.enemy_type == EnemyData.EnemyType.CHARGER, "敵人類型設置錯誤")
	assert(charger_data.move_speed == 250.0, "移動速度設置錯誤")
	
	print("✓ 敵人數據資源創建測試通過")

func _test_ai_state_machine() -> void:
	print("測試2: AI狀態機系統")
	
	# 創建測試敵人
	var test_enemy = CharacterBody2D.new()
	add_child(test_enemy)
	
	# 創建AI狀態機
	var ai_state_machine = EnemyAIStateMachine.new()
	ai_state_machine.enemy = test_enemy
	
	# 創建測試敵人數據
	var test_data = EnemyData.new()
	test_data.enemy_type = EnemyData.EnemyType.TRACKER
	test_data.detection_range = 100.0
	test_data.attack_range = 50.0
	
	ai_state_machine.enemy_data = test_data
	test_enemy.add_child(ai_state_machine)
	
	# 測試狀態變化
	var state_changed_called = false
	ai_state_machine.state_changed.connect(func(old_state, new_state): state_changed_called = true)
	
	# 測試初始狀態
	assert(ai_state_machine.get_current_state() == EnemyAIStateMachine.State.IDLE, "初始狀態錯誤")
	
	# 測試狀態變化
	ai_state_machine.change_state(EnemyAIStateMachine.State.PATROL)
	assert(ai_state_machine.get_current_state() == EnemyAIStateMachine.State.PATROL, "狀態變化失敗")
	assert(state_changed_called, "狀態變化信號未觸發")
	
	# 測試眩暈狀態
	ai_state_machine.set_stunned(1.0)
	assert(ai_state_machine.get_current_state() == EnemyAIStateMachine.State.STUNNED, "眩暈狀態設置失敗")
	
	# 清理
	test_enemy.queue_free()
	
	print("✓ AI狀態機系統測試通過")

func _test_enemy_type_system() -> void:
	print("測試3: 敵人類型系統")
	
	# 測試敵人類型枚舉
	assert(EnemyData.EnemyType.TRACKER == 0, "追蹤型枚舉值錯誤")
	assert(EnemyData.EnemyType.SHOOTER == 1, "射擊型枚舉值錯誤")
	assert(EnemyData.EnemyType.CHARGER == 2, "衝鋒型枚舉值錯誤")
	
	# 測試敵人數據類型轉換
	var tracker_data = EnemyData.get_default_data(EnemyData.EnemyType.TRACKER)
	var shooter_data = EnemyData.get_default_data(EnemyData.EnemyType.SHOOTER)
	var charger_data = EnemyData.get_default_data(EnemyData.EnemyType.CHARGER)
	
	assert(tracker_data.enemy_type == EnemyData.EnemyType.TRACKER, "追蹤型數據類型錯誤")
	assert(shooter_data.enemy_type == EnemyData.EnemyType.SHOOTER, "射擊型數據類型錯誤")
	assert(charger_data.enemy_type == EnemyData.EnemyType.CHARGER, "衝鋒型數據類型錯誤")
	
	print("✓ 敵人類型系統測試通過")

func _test_projectile_system() -> void:
	print("測試4: 投射物系統")
	
	# 創建測試投射物
	var projectile = EnemyProjectile.new()
	add_child(projectile)
	
	# 測試投射物初始化
	var test_direction = Vector2(1, 0)
	projectile.initialize(test_direction, 200.0)
	
	assert(projectile.direction == test_direction.normalized(), "投射物方向設置錯誤")
	assert(projectile.speed == 200.0, "投射物速度設置錯誤")
	
	# 測試投射物屬性設置
	projectile.set_projectile_properties(300.0, 25.0)
	assert(projectile.speed == 300.0, "投射物速度更新失敗")
	assert(projectile.damage == 25.0, "投射物傷害更新失敗")
	
	# 清理
	projectile.queue_free()
	
	print("✓ 投射物系統測試通過")

# 手動測試方法
func test_enemy_spawning() -> void:
	print("手動測試: 敵人生成系統")
	
	# 創建測試世界
	_create_test_world()
	
	# 創建測試玩家
	_create_test_player()
	
	# 創建測試敵人
	_create_test_enemies()
	
	print("手動測試設置完成，請在遊戲中觀察敵人行為")

func _create_test_world() -> void:
	_test_world = Node2D.new()
	_test_world.name = "TestWorld"
	add_child(_test_world)

func _create_test_player() -> void:
	_test_player = CharacterBody2D.new()
	_test_player.name = "TestPlayer"
	_test_player.add_to_group("player_group")
	_test_player.global_position = Vector2(500, 500)
	_test_world.add_child(_test_player)

func _create_test_enemies() -> void:
	# 創建追蹤型敵人
	var tracker_enemy = _create_test_enemy(EnemyData.EnemyType.TRACKER, Vector2(400, 400))
	_test_enemies.append(tracker_enemy)
	
	# 創建射擊型敵人
	var shooter_enemy = _create_test_enemy(EnemyData.EnemyType.SHOOTER, Vector2(600, 400))
	_test_enemies.append(shooter_enemy)
	
	# 創建衝鋒型敵人
	var charger_enemy = _create_test_enemy(EnemyData.EnemyType.CHARGER, Vector2(500, 300))
	_test_enemies.append(charger_enemy)

func _create_test_enemy(enemy_type: EnemyData.EnemyType, position: Vector2) -> Node2D:
	var enemy = CharacterBody2D.new()
	enemy.name = "TestEnemy_" + str(enemy_type)
	enemy.global_position = position
	
	# 添加碰撞形狀
	var collision = CollisionShape2D.new()
	var shape = RectangleShape2D.new()
	shape.size = Vector2(32, 32)
	collision.shape = shape
	enemy.add_child(collision)
	
	# 添加精靈
	var sprite = Sprite2D.new()
	var texture = GradientTexture2D.new()
	var gradient = Gradient.new()
	gradient.colors = [Color.RED, Color.DARK_RED]
	texture.gradient = gradient
	texture.width = 32
	texture.height = 32
	sprite.texture = texture
	enemy.add_child(sprite)
	
	# 設置敵人數據
	var enemy_data = EnemyData.get_default_data(enemy_type)
	var enemy_script = enemy.attach_script(load("res://scripts/enemies/enemy.gd"))
	enemy.set_script(enemy_script)
	enemy.set("enemy_data", enemy_data)
	
	_test_world.add_child(enemy)
	return enemy

# 清理測試環境
func cleanup_test_environment() -> void:
	if _test_world != null:
		_test_world.queue_free()
		_test_world = null
	
	_test_enemies.clear()
	_test_player = null
	
	print("測試環境已清理")
