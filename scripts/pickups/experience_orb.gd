extends Area2D

## Experience orb that can be collected by the player.
## Drops from enemies when they die and provides experience points.
## 支援物件池回收。

signal collected(amount: int)

@export var experience_amount: int = 1
@export var attraction_speed: float = 300.0  # Increased speed for better effect
@export var attraction_range: float = 150.0  # Increased range

var _player: Node2D
var _is_attracted: bool = false
var _original_scale: Vector2
var _pulse_time: float = 0.0

func _ready() -> void:
	# Find the player node
	_player = get_tree().get_first_node_in_group("player_group")

	# Store original scale for animation
	_original_scale = $Sprite2D.scale
	set_deferred("monitoring", true)

func _physics_process(delta: float) -> void:
	if _is_attracted and _player != null:
		_attract_to_player(delta)
		# Update pulse animation
		_pulse_time += delta
	elif _player != null:
		_check_attraction_range()
	
	# Update visual effects
	_update_visual_effects()

func _check_attraction_range() -> void:
	if _player == null:
		return
	
	var distance: float = global_position.distance_to(_player.global_position)
	if distance <= attraction_range:
		_start_attraction()

func _start_attraction() -> void:
	if not _is_attracted:
		_is_attracted = true
		# Show attraction effect
		$AttractionEffect.visible = true
		_pulse_time = 0.0  # Reset pulse time

func _attract_to_player(delta: float) -> void:
	if _player == null:
		return
	
	var direction: Vector2 = global_position.direction_to(_player.global_position)
	var distance: float = global_position.distance_to(_player.global_position)
	
	# If within attraction range, move towards player
	if distance > 10.0:  # Don't get too close to avoid jittering
		# Smooth attraction with easing - speed increases as it gets closer
		var speed_multiplier = 1.0 + (attraction_range - distance) / attraction_range
		var attraction_force = direction * attraction_speed * speed_multiplier * delta
		global_position += attraction_force
	else:
		# Close enough, collect immediately
		collect()

func _update_visual_effects() -> void:
	if _is_attracted:
		# Add a slight glow effect
		$Sprite2D.modulate = Color(0.6, 1.0, 1.0, 1.0)  # Brighter glow
		# Make the sprite slightly larger when attracted with pulse effect
		var pulse_scale = _original_scale * (1.3 + 0.15 * sin(_pulse_time * PI * 2 / 0.3))
		$Sprite2D.scale = pulse_scale
	else:
		# Normal color and scale
		$Sprite2D.modulate = Color(0.2, 0.8, 1.0, 1.0)
		$Sprite2D.scale = _original_scale

func collect() -> void:
	collected.emit(experience_amount)
	_return_to_pool()

# 物件池回調與回收
func on_taken_from_pool() -> void:
	_is_attracted = false
	$AttractionEffect.visible = false
	$Sprite2D.scale = _original_scale
	set_deferred("monitoring", true)

func on_returned_to_pool() -> void:
	set_deferred("monitoring", false)

func _return_to_pool() -> void:
	var pool := get_tree().root.get_node_or_null("NodePoolManager")
	if pool:
		pool.return_node(self)
	else:
		queue_free()
