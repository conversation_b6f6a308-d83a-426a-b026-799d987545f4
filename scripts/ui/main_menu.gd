extends Control

## 主選單控制器，處理選單按鈕的點擊事件

func _ready() -> void:
	# 確保遊戲沒有暫停
	get_tree().paused = false

	# Programmatically connect signals to ensure they are set up correctly.
	var start_button = find_child("StartGameButton")
	if start_button and not start_button.pressed.is_connected(_on_start_game_button_pressed):
		start_button.pressed.connect(_on_start_game_button_pressed)

	var upgrades_button = find_child("UpgradesButton")
	if upgrades_button and not upgrades_button.pressed.is_connected(_on_upgrades_button_pressed):
		upgrades_button.pressed.connect(_on_upgrades_button_pressed)

	var settings_button = find_child("SettingsButton")
	if settings_button and not settings_button.pressed.is_connected(_on_settings_button_pressed):
		settings_button.pressed.connect(_on_settings_button_pressed)

	var exit_button = find_child("QuitGameButton")
	if exit_button and not exit_button.pressed.is_connected(_on_exit_button_pressed):
		exit_button.pressed.connect(_on_exit_button_pressed)

func _on_start_game_button_pressed() -> void:
	"""開始遊戲按鈕點擊事件"""
	print("開始遊戲按鈕被點擊")
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_ui_click()
	GameManager.start_game()

func _on_exit_button_pressed():
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_ui_click()
	get_tree().quit()


func _on_upgrades_button_pressed():
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_ui_click()
	GameManager.switch_scene("res://scenes/ui/meta_upgrade_screen.tscn")


func _on_settings_button_pressed():
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_ui_click()
	var settings = find_child("SettingsMenu")
	if settings:
		settings.show()
