extends CanvasLayer
class_name LevelUpScreen

## Level up screen that appears when the player levels up.
## Pauses the game and allows the player to choose upgrades.

@onready var option1_button: Button = $CenterContainer/Panel/VBoxContainer/OptionsContainer/Option1Button
@onready var option2_button: Button = $CenterContainer/Panel/VBoxContainer/OptionsContainer/Option2Button
@onready var option3_button: Button = $CenterContainer/Panel/VBoxContainer/OptionsContainer/Option3Button
@onready var continue_button: Button = $CenterContainer/Panel/VBoxContainer/ContinueButton

## 升級選項按鈕陣列
var upgrade_buttons: Array[Button] = []

## 信號：當玩家選擇升級選項時發出
signal upgrade_selected(upgrade_data: Resource)

func _ready() -> void:
	# 初始化升級按鈕陣列
	upgrade_buttons = [option1_button, option2_button, option3_button]
	
	# 連接按鈕信號
	for button in upgrade_buttons:
		button.pressed.connect(_on_upgrade_option_pressed.bind(button))
	
	continue_button.pressed.connect(_on_continue_pressed)
	
	# 連接到 LevelManager
	if LevelManager != null:
		LevelManager.level_up.connect(_on_level_up)

func _on_level_up(_new_level: int) -> void:
	# 暫停遊戲
	get_tree().paused = true
	
	# 從 LevelManager 獲取升級選項
	var upgrade_options = LevelManager.get_upgrade_options(3)
	
	# 更新升級選項按鈕
	_update_upgrade_buttons(upgrade_options)
	
	# 顯示升級畫面
	visible = true

## 更新升級選項按鈕
func _update_upgrade_buttons(upgrade_options: Array) -> void:
	print("正在更新升級按鈕，收到 ", upgrade_options.size(), " 個選項")
	
	# 重置所有按鈕
	for i in range(upgrade_buttons.size()):
		var button = upgrade_buttons[i]
		button.text = "無可用升級"
		button.disabled = true
		button.set_meta("upgrade_data", null)
	
	# 設置可用的升級選項
	for i in range(min(upgrade_options.size(), upgrade_buttons.size())):
		var upgrade_data = upgrade_options[i]
		var button = upgrade_buttons[i]
		
		if upgrade_data:
			print("處理升級選項 ", i, ": ", upgrade_data)
			print("  - 類型: ", upgrade_data.get_class())
			print("  - 名稱: ", upgrade_data.upgrade_name if upgrade_data.has_method("get") else "無法獲取名稱")
			
			button.text = upgrade_data.upgrade_name
			button.disabled = false
			button.set_meta("upgrade_data", upgrade_data)
			
			# 添加工具提示
			if upgrade_data.description:
				button.tooltip_text = upgrade_data.description

## 處理升級選項按鈕點擊
func _on_upgrade_option_pressed(button: Button) -> void:
	var upgrade_data = button.get_meta("upgrade_data")
	if upgrade_data:
		# 發出升級選擇信號
		upgrade_selected.emit(upgrade_data)
		print("玩家選擇了升級：", upgrade_data.upgrade_name)
	else:
		print("錯誤：按鈕沒有關聯的升級數據")
	
	_hide_screen()

func _on_continue_pressed() -> void:
	_hide_screen()

func _hide_screen() -> void:
	visible = false
	get_tree().paused = false
