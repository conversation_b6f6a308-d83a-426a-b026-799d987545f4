extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var currency_label: Label = %CurrencyLabel
@onready var health_label: Label = %HealthLabel
@onready var speed_label: Label = %SpeedLabel

# Define costs for upgrades
const HEALTH_UPGRADE_COST = 100
const SPEED_UPGRADE_COST = 150


func _ready():
	update_labels()


func update_labels():
	var current_stats = SaveManager.stats
	if not current_stats:
		printerr("SaveManager stats not available!")
		return
		
	currency_label.text = "Soul Coins: %d" % current_stats.meta_currency
	health_label.text = "Bonus Health: +%d" % current_stats.bonus_health
	speed_label.text = "Bonus Speed: +%.1f" % current_stats.bonus_move_speed


func _on_back_button_pressed():
	GameManager.switch_scene("res://scenes/ui/main_menu.tscn")


func _on_upgrade_health_button_pressed():
	var current_stats = SaveManager.stats
	if current_stats.meta_currency >= HEALTH_UPGRADE_COST:
		current_stats.meta_currency -= HEALTH_UPGRADE_COST
		current_stats.bonus_health += 10
		SaveManager.save_stats()
		update_labels()
	else:
		# Optional: Add feedback to the player, like a sound or animation
		print("Not enough currency for health upgrade.")


func _on_upgrade_speed_button_pressed():
	var current_stats = SaveManager.stats
	if current_stats.meta_currency >= SPEED_UPGRADE_COST:
		current_stats.meta_currency -= SPEED_UPGRADE_COST
		current_stats.bonus_move_speed += 5.0
		SaveManager.save_stats()
		update_labels()
	else:
		# Optional: Add feedback to the player
		print("Not enough currency for speed upgrade.")
