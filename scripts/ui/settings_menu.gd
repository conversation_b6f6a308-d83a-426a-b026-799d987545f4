extends CanvasLayer

## 設置選單：提供 SFX/BGM 音量滑桿

@onready var _sfx_slider: HSlider = $Panel/VBoxContainer/SFXRow/SFXSlider
@onready var _bgm_slider: HSlider = $Panel/VBoxContainer/BGMRow/BGMSlider
@onready var _close_button: Button = $Panel/VBoxContainer/Buttons/CloseButton

func _ready() -> void:
	# 初始化滑桿範圍（dB）
	for s in [_sfx_slider, _bgm_slider]:
		if s:
			s.min_value = -30.0
			s.max_value = 6.0
			s.step = 0.5

	# 從 AudioManager 讀取目前音量
	var am = get_node_or_null("/root/AudioManager")
	if am:
		_sfx_slider.value = am.get_sfx_volume_db()
		_bgm_slider.value = am.get_bgm_volume_db()
	else:
		# 預設 0 dB
		_sfx_slider.value = 0.0
		_bgm_slider.value = 0.0

	# 連接事件
	if not _sfx_slider.value_changed.is_connected(_on_sfx_value_changed):
		_sfx_slider.value_changed.connect(_on_sfx_value_changed)
	if not _bgm_slider.value_changed.is_connected(_on_bgm_value_changed):
		_bgm_slider.value_changed.connect(_on_bgm_value_changed)
	if not _close_button.pressed.is_connected(_on_close_button_pressed):
		_close_button.pressed.connect(_on_close_button_pressed)

func _on_sfx_value_changed(value: float) -> void:
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.set_sfx_volume(value)

func _on_bgm_value_changed(value: float) -> void:
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.set_bgm_volume(value)

func _on_close_button_pressed() -> void:
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_ui_click()
	hide()

