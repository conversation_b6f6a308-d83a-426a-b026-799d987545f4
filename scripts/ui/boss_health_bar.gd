extends HBoxContainer

## A UI component to display the boss's multi-stage health.

@export var health_bar_scene: PackedScene

var _boss_health_component: BossHealthComponent

func _ready() -> void:
	pass # Boss health component will be set externally

func set_boss_health_component(component: BossHealthComponent) -> void:
	if component == null:
		push_error("Attempted to set null BossHealthComponent")
		return
	_boss_health_component = component
	_boss_health_component.stage_cleared.connect(_on_stage_cleared)
	_setup_health_bars()

func _process(_delta: float) -> void:
	if _boss_health_component:
		_update_health_bars()

func _setup_health_bars() -> void:
	# Clear any existing health bars
	for child in get_children():
		child.queue_free()

	if not health_bar_scene:
		push_error("BossHealthBar UI: health_bar_scene is not set in the editor!")
		return

	# Create a health bar for each stage in reverse order (for right-to-left display)
	for i in range(_boss_health_component.health_stages.size() - 1, -1, -1):
		var health_bar = health_bar_scene.instantiate()
		add_child(health_bar)

func _update_health_bars() -> void:
	if not _boss_health_component:
		return

	var current_active_stage_index = _boss_health_component.current_stage
	var total_stages = _boss_health_component.health_stages.size()
	
	for i in range(get_child_count()):
		var health_bar_node = get_child(i)
		if not health_bar_node:
			continue

		# Map the child index (i) to the actual stage index.
		# Since bars are added in reverse order, child 0 is the last stage, child (total_stages-1) is the first.
		var mapped_stage_index = (total_stages - 1) - i

		# Directly set the health values on the child health bar nodes.
		# This leverages the child's own drawing logic instead of manually controlling it,
		# which is a more robust approach.
		var max_hp = 1.0
		var current_hp = 0.0

		if mapped_stage_index < current_active_stage_index:
			# This stage is cleared, show an empty bar
			current_hp = 0.0
		elif mapped_stage_index == current_active_stage_index:
			# This is the active stage
			current_hp = _boss_health_component.current_health
			max_hp = _boss_health_component.health_stages[mapped_stage_index]
		else:
			# This is a future stage, show a full bar
			current_hp = 1.0
		
		# Set the values on the child node. The child's own script will handle the update.
		# This avoids duplicating logic or making assumptions about the child's internal nodes.
		if health_bar_node.has_method("connect_to_health_component"):
			# We can't use the connect method as it expects a real HealthComponent.
			# Instead, we directly set the internal health variables.
			health_bar_node._current_health = current_hp
			health_bar_node._max_health = max_hp
		
		# Manually call the child's update function since its own _process loop is inactive
		# (because we never connected a real HealthComponent).
		if health_bar_node.has_method("_update_health_bar"):
			health_bar_node._update_health_bar()

func _on_stage_cleared(stage_index: int) -> void:
	# Update the health bars when a stage is cleared
	_update_health_bars()
