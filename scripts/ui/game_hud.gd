extends Can<PERSON>Layer

## Game HUD that displays player level and experience information.
## Connects to LevelManager to receive updates.

@onready var level_label: Label = $HUDContainer/TopLeftPanel/VBoxContainer/LevelLabel
@onready var experience_bar: ProgressBar = $HUDContainer/TopLeftPanel/VBoxContainer/ExperienceBar
@onready var experience_label: Label = $HUDContainer/TopLeftPanel/VBoxContainer/ExperienceLabel

func _ready() -> void:
	# Connect to LevelManager signals
	if LevelManager != null:
		LevelManager.level_up.connect(_on_level_up)
		LevelManager.experience_updated.connect(_on_experience_updated)
		
		# Initialize with current values
		_update_display(LevelManager.get_current_level(), LevelManager.get_current_xp(), LevelManager.get_required_xp())

func _on_level_up(new_level: int) -> void:
	_update_display(new_level, LevelManager.get_current_xp(), LevelManager.get_required_xp())

func _on_experience_updated(current_xp: int, required_xp: int) -> void:
	_update_display(LevelManager.get_current_level(), current_xp, required_xp)

func _update_display(level: int, current_xp: int, required_xp: int) -> void:
	level_label.text = "Level: %d" % level
	experience_bar.max_value = required_xp
	experience_bar.value = current_xp
	experience_label.text = "%d / %d XP" % [current_xp, required_xp]


func show_boss_health_bar(boss_health_component: Node) -> void:
	var boss_health_bar = $HUDContainer/BossHealthBar
	if boss_health_bar and boss_health_bar.has_method("set_boss_health_component"):
		# --- Reposition and resize the health bar to be more prominent ---
		# Use the center-top preset to anchor its center to the top-center of the screen.
		boss_health_bar.set_anchors_preset(Control.PRESET_CENTER_TOP)
		# Add a vertical margin for spacing from the top edge.
		boss_health_bar.position.y = 50
		# Scale the health bar up to make it larger and more visible.
		boss_health_bar.scale = Vector2(1.5, 1.5)
		# --- End of repositioning ---

		boss_health_bar.set_boss_health_component(boss_health_component)
		boss_health_bar.show()

func hide_boss_health_bar() -> void:
	var boss_health_bar = $HUDContainer/BossHealthBar
	if boss_health_bar:
		boss_health_bar.hide()