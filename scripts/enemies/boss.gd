extends "res://scripts/enemies/enemy.gd"
class_name Boss

## Boss-specific logic, including attack patterns and stage transitions.

enum BossState { IDLE, ATTACKING, STUNNED, DEFEATED }

@export var projectile_scene: PackedScene
var experience_orb_scene_override: PackedScene = preload("res://scenes/pickups/experience_orb.tscn")
@export var death_reward_experience: int = 1000

var boss_state: BossState = BossState.IDLE
var _boss_health_component: BossHealthComponent

func _ready() -> void:
	# Call the parent's ready function
	super._ready()

	_boss_health_component = find_child("BossHealthComponent")
	if _boss_health_component:
		if not _boss_health_component.stage_cleared.is_connected(_on_stage_cleared):
			_boss_health_component.stage_cleared.connect(_on_stage_cleared)
		if not _boss_health_component.boss_defeated.is_connected(_on_boss_defeated):
			_boss_health_component.boss_defeated.connect(_on_boss_defeated)

# Override take_damage to pass it to the correct component.
func take_damage(amount: float) -> void:
	if boss_state == BossState.DEFEATED:
		return
	
	if _boss_health_component:
		_boss_health_component.take_damage(amount)
		# Optionally, show damage numbers or play hurt sounds like in the base enemy
		_show_damage_number(amount)
		if enemy_data and enemy_data.hurt_sound:
			play_sound(enemy_data.hurt_sound)
	else:
		push_error("Boss cannot take damage: _boss_health_component is not set. Check if the node exists in boss.tscn and is named correctly.")

func _show_damage_number(amount: float) -> void:
	# 創建傷害數字標籤
	var damage_label = Label.new()
	damage_label.text = str(int(amount))
	damage_label.add_theme_color_override("font_color", Color.RED)
	damage_label.add_theme_font_size_override("font_size", 24)
	damage_label.global_position = global_position + Vector2(randf_range(-20, 20), -50)
	
	# 添加到場景
	var current_scene = get_tree().current_scene
	if current_scene != null:
		current_scene.add_child(damage_label)
	
	# 創建動畫
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(damage_label, "global_position:y", damage_label.global_position.y - 50, 0.8).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_OUT)
	tween.tween_property(damage_label, "modulate:a", 0.0, 0.8).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN)
	
	# 動畫完成後移除
	tween.finished.connect(func(): damage_label.queue_free())

func _physics_process(delta: float) -> void:
	# The AI state machine runs on its own via its _process function.
	# We just need to get the velocity it has calculated.
	if _ai_state_machine and (boss_state == BossState.IDLE or boss_state == BossState.ATTACKING):
		velocity = _ai_state_machine.enemy.velocity
	
	# Call the parent's physics process which includes move_and_slide()
	super._physics_process(delta)

	match boss_state:
		BossState.IDLE:
			# If player is in attack range, start the attack timer.
			# The AI state machine will continue to provide chase velocity if not in range.
			if _ai_state_machine and _ai_state_machine.is_in_attack_range():
				if $AttackTimer.is_stopped():
					$AttackTimer.start()
		BossState.ATTACKING:
			# When attacking, we stop moving. The attack itself is triggered by the timer.
			velocity = Vector2.ZERO
		BossState.STUNNED:
			velocity = Vector2.ZERO
		BossState.DEFEATED:
			velocity = Vector2.ZERO


func _execute_attack() -> void:
	if boss_state != BossState.ATTACKING:
		return

	match _boss_health_component.current_stage:
		0:
			_attack_stage_0()
		1:
			_attack_stage_1()
		2:
			_attack_stage_2()
	
	# After attacking, go back to idle to decide the next move
	boss_state = BossState.IDLE


func _attack_stage_0() -> void:
	# Fires a single projectile at the player.
	if not _player:
		return
	var direction = ( _player.global_position - global_position).normalized()
	_spawn_projectile(direction)

func _attack_stage_1() -> void:
	# Fires a burst of three projectiles at the player.
	if not _player:
		return
	var base_direction = ( _player.global_position - global_position).normalized()
	for i in range(-1, 2):
		var offset = i * 0.2 # 11.5 degrees
		_spawn_projectile(base_direction.rotated(offset))

func _attack_stage_2() -> void:
	# Fires a circular volley of projectiles.
	var num_projectiles = 12
	for i in range(num_projectiles):
		var angle = 2 * PI * i / num_projectiles
		_spawn_projectile(Vector2.RIGHT.rotated(angle))

func _spawn_projectile(direction: Vector2) -> void:
	if not projectile_scene:
		return
	var projectile = projectile_scene.instantiate() as EnemyProjectile
	get_tree().current_scene.add_child(projectile)
	projectile.global_position = global_position
	projectile.initialize(direction)

func _on_stage_cleared(stage_index: int) -> void:
	print("Boss stage %d cleared!" % stage_index)
	# Add effects for stage transition, like a temporary stun
	boss_state = BossState.STUNNED
	$StunTimer.start()

func _on_boss_defeated() -> void:
	print("Boss defeated!")
	boss_state = BossState.DEFEATED
	# Call the original death logic from enemy.gd
	super._on_health_component_died()
	_spawn_death_reward()

func _spawn_death_reward() -> void:
	if not experience_orb_scene_override:
		return

	var num_orbs = 50
	for i in range(num_orbs):
		var orb = experience_orb_scene_override.instantiate()
		get_tree().root.call_deferred("add_child", orb)
		orb.global_position = global_position + Vector2(randf_range(-100, 100), randf_range(-100, 100))
		orb.experience_amount = death_reward_experience / num_orbs

func _on_attack_timer_timeout() -> void:
	boss_state = BossState.ATTACKING
	_execute_attack()

func _on_stun_timer_timeout() -> void:
	boss_state = BossState.IDLE
