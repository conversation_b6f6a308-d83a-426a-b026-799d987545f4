extends Node
class_name EnemyAIStateMachine

## 敵人AI狀態機
## 管理敵人的不同行為狀態

enum State {
	IDLE,       # 閒置狀態
	PATROL,     # 巡邏狀態
	CHASE,      # 追蹤狀態
	ATTACK,     # 攻擊狀態
	RETREAT,    # 撤退狀態
	STUNNED     # 眩暈狀態
}

signal state_changed(old_state: State, new_state: State)

@export var enemy: CharacterBody2D
@export var enemy_data: EnemyData

var current_state: State = State.IDLE
var player: Node2D
var target_position: Vector2
var state_timer: float = 0.0
var last_attack_time: float = 0.0

# 狀態特定的變數
var patrol_points: Array[Vector2] = []
var current_patrol_index: int = 0
var charge_target: Vector2
var charge_cooldown: float = 0.0

# 群體行為變數
var _nearby_enemies: Array[Node2D] = []
var _group_behavior_enabled: bool = true
var _avoidance_force: Vector2 = Vector2.ZERO

# 智能攻擊變數


var _attack_cooldown_timer: float = 0.0
var _tactical_retreat_timer: float = 0.0

# 環境感知變數
var _obstacles: Array[Node2D] = []
var _environment_hazards: Array[Node2D] = []
var _pathfinding_nodes: Array[Node2D] = []
var _environment_awareness: bool = true


# 學習和適應變數

var _successful_attacks: int = 0
var _failed_attacks: int = 0
var _adaptation_level: float = 0.0
var _last_player_position: Vector2 = Vector2.ZERO
var _player_movement_pattern: Array[Vector2] = []

func _ready() -> void:
	if enemy == null:
		push_error("EnemyAIStateMachine: 敵人節點未設置")
		return
	
	# 尋找玩家
	player = get_tree().get_first_node_in_group("player_group")
	
	# 初始化巡邏點
	_setup_patrol_points()
	
	# 開始閒置狀態
	change_state(State.IDLE)

func _process(delta: float) -> void:
	if enemy == null or enemy_data == null:
		return
	
	state_timer += delta
	
	# 更新群體行為
	if _group_behavior_enabled:
		_update_group_behavior(delta)
	
	# 更新當前狀態
	_update_current_state(delta)
	
	# 檢查狀態轉換條件
	_check_state_transitions()

func change_state(new_state: State) -> void:
	if new_state == current_state:
		return
	
	var old_state = current_state
	current_state = new_state
	state_timer = 0.0
	
	# 進入新狀態
	_enter_state(new_state)
	
	# 發送信號
	state_changed.emit(old_state, new_state)

func _enter_state(state: State) -> void:
	match state:
		State.IDLE:
			# 閒置狀態：隨機等待
			pass
			
		State.PATROL:
			# 巡邏狀態：移動到下一個巡邏點
			_move_to_patrol_point()
			
		State.CHASE:
			# 追蹤狀態：朝著玩家移動
			pass
			
		State.ATTACK:
			# 攻擊狀態：執行攻擊
			_execute_attack()
			
		State.RETREAT:
			# 撤退狀態：遠離玩家
			pass
			
		State.STUNNED:
			# 眩暈狀態：無法行動
			pass

func _update_current_state(delta: float) -> void:
	match current_state:
		State.IDLE:
			_update_idle_state(delta)
			
		State.PATROL:
			_update_patrol_state(delta)
			
		State.CHASE:
			_update_chase_state(delta)
			
		State.ATTACK:
			_update_attack_state(delta)
			
		State.RETREAT:
			_update_retreat_state(delta)
			
		State.STUNNED:
			_update_stunned_state(delta)

func _check_state_transitions() -> void:
	if player == null:
		return
	
	var distance_to_player = enemy.global_position.distance_to(player.global_position)
	
	# 檢查是否在偵測範圍內
	if distance_to_player <= enemy_data.detection_range:
		# 在偵測範圍內，根據敵人類型決定行為
		match enemy_data.enemy_type:
			EnemyData.EnemyType.TRACKER:
				if distance_to_player <= enemy_data.attack_range:
					change_state(State.ATTACK)
				else:
					change_state(State.CHASE)
					
			EnemyData.EnemyType.SHOOTER:
				if distance_to_player <= enemy_data.attack_range:
					change_state(State.ATTACK)
				else:
					change_state(State.CHASE)
					
			EnemyData.EnemyType.CHARGER:
				if distance_to_player <= enemy_data.attack_range:
					change_state(State.ATTACK)
				elif distance_to_player <= enemy_data.detection_range * 0.7:
					change_state(State.CHASE)
				else:
					change_state(State.PATROL)
	else:
		# 不在偵測範圍內，回到巡邏狀態
		if current_state != State.PATROL and current_state != State.IDLE:
			change_state(State.PATROL)

func _setup_patrol_points() -> void:
	# 在敵人周圍創建巡邏點
	var center = enemy.global_position
	var radius = 100.0
	
	for i in range(4):
		var angle = i * PI / 2
		var point = center + Vector2(cos(angle), sin(angle)) * radius
		patrol_points.append(point)

func _move_to_patrol_point() -> void:
	if patrol_points.size() == 0:
		return
		
	target_position = patrol_points[current_patrol_index]
	var direction = enemy.global_position.direction_to(target_position)
	enemy.velocity = direction * enemy_data.move_speed * 0.5  # 巡邏時速度較慢

func _execute_attack() -> void:
	var current_time = Time.get_ticks_msec() / 1000.0
	
	if current_time - last_attack_time < enemy_data.damage_cooldown:
		return
		
	last_attack_time = current_time
	
	# 根據敵人類型執行不同攻擊
	match enemy_data.enemy_type:
		EnemyData.EnemyType.TRACKER:
			_execute_tracker_attack()
		EnemyData.EnemyType.SHOOTER:
			_execute_shooter_attack()
		EnemyData.EnemyType.CHARGER:
			_execute_charger_attack()

func _execute_shooter_attack() -> void:
	# 射擊型敵人：發射投射物
	if player != null:
		# 射擊型敵人的攻擊由具體的敵人腳本處理
		# 這裡只負責狀態管理，不執行具體攻擊
		pass

func _execute_charger_attack() -> void:
	# 衝鋒型敵人：衝鋒攻擊
	if player != null:
		charge_target = player.global_position
		# 衝鋒型敵人的攻擊由具體的敵人腳本處理
		# 這裡只負責狀態管理，不執行具體攻擊
		# 衝鋒完成後會自動回到追蹤狀態

# 狀態更新函數
func _update_idle_state(_delta: float) -> void:
	# 閒置狀態：隨機等待
	if state_timer > randf_range(1.0, 3.0):
		change_state(State.PATROL)

func _update_patrol_state(_delta: float) -> void:
	# 巡邏狀態：移動到巡邏點
	if enemy.global_position.distance_to(target_position) < 10.0:
		current_patrol_index = (current_patrol_index + 1) % patrol_points.size()
		_move_to_patrol_point()

func _update_chase_state(_delta: float) -> void:
	# 追蹤狀態：朝著玩家移動
	if player != null:
		var direction = enemy.global_position.direction_to(player.global_position)
		
		# 檢查路徑是否被阻擋
		if _is_path_blocked(direction):
			# 尋找替代路徑
			direction = _find_alternative_path()
		
		enemy.velocity = direction * enemy_data.move_speed

func _update_attack_state(delta: float) -> void:
	# 攻擊狀態：停止移動，面向玩家
	if player != null:
		var direction = enemy.global_position.direction_to(player.global_position)
		enemy.velocity = Vector2.ZERO
		
		# 面向玩家
		if enemy.has_method("set_rotation"):
			enemy.rotation = direction.angle()
		elif enemy.has_method("set_flip_h"):
			# 如果沒有rotation方法，使用翻轉來面向玩家
			enemy.set_flip_h(direction.x < 0)
		
		# 智能攻擊模式
		_update_intelligent_attack(delta)

func _update_intelligent_attack(delta: float) -> void:
	"""更新智能攻擊邏輯"""
	if player == null:
		return
	
	# 更新攻擊冷卻計時器
	_attack_cooldown_timer += delta
	
	# 檢查攻擊冷卻
	if _attack_cooldown_timer < enemy_data.damage_cooldown:
		return
	
	# 根據敵人類型選擇攻擊模式
	match enemy_data.enemy_type:
		EnemyData.EnemyType.TRACKER:
			_execute_tactical_tracker_attack()
		EnemyData.EnemyType.SHOOTER:
			_execute_tactical_shooter_attack()
		EnemyData.EnemyType.CHARGER:
			_execute_tactical_charger_attack()
	
	# 重置攻擊冷卻計時器
	_attack_cooldown_timer = 0.0

func _execute_tactical_tracker_attack() -> void:
	"""執行戰術性追蹤型敵人攻擊（改進版）"""
	if player == null:
		return
	
	# 預測玩家移動
	var predicted_position = _predict_player_movement()
	var distance_to_predicted = enemy.global_position.distance_to(predicted_position)
	
	# 如果預測位置太遠，使用當前位置
	if distance_to_predicted > enemy_data.attack_range * 2:
		predicted_position = player.global_position
	
	# 根據適應等級調整攻擊策略
	var _attack_strategy = _choose_attack_strategy()
	
	# 移動到攻擊位置
	var direction = enemy.global_position.direction_to(predicted_position)
	enemy.velocity = direction * enemy_data.move_speed * 0.8
	
	# 檢查是否到達攻擊位置
	if enemy.global_position.distance_to(predicted_position) <= enemy_data.attack_range:
		# 執行攻擊
		var attack_successful = _execute_tracker_attack()
		
		# 記錄攻擊結果
		if attack_successful:
			_successful_attacks += 1
		else:
			_failed_attacks += 1
		
		# 攻擊後戰術撤退
		_start_tactical_retreat()

func _execute_tactical_shooter_attack() -> void:
	"""執行戰術性射擊型敵人攻擊"""
	if player == null:
		return
	
	# 射擊型敵人的攻擊由具體的敵人腳本處理
	# 這裡只負責狀態管理和戰術決策
	
	# 檢查是否需要戰術撤退
	if _should_tactical_retreat():
		_start_tactical_retreat()

func _execute_tactical_charger_attack() -> void:
	"""執行戰術性衝鋒型敵人攻擊"""
	if player == null:
		return
	
	# 衝鋒型敵人的攻擊由具體的敵人腳本處理
	# 這裡只負責狀態管理和戰術決策
	
	# 檢查是否需要戰術撤退
	if _should_tactical_retreat():
		_start_tactical_retreat()

func _predict_player_movement() -> Vector2:
	"""預測玩家移動位置（改進版，使用學習到的模式）"""
	if player == null:
		return Vector2.ZERO
	
	# 基礎預測
	var base_prediction = _predict_player_movement_basic()
	
	# 如果沒有足夠的學習數據，使用基礎預測
	if _player_movement_pattern.size() < 3:
		return base_prediction
	
	# 使用學習到的模式進行預測
	var learned_prediction = _predict_player_movement_learned()
	
	# 根據適應等級混合兩種預測
	var adaptation_weight = (_adaptation_level + 1.0) / 2.0  # 轉換到 0-1 範圍
	return base_prediction.lerp(learned_prediction, adaptation_weight)

func _predict_player_movement_basic() -> Vector2:
	"""基礎玩家移動預測"""
	if player == null:
		return Vector2.ZERO
	
	var player_velocity = Vector2.ZERO
	if player.has_method("get_velocity"):
		player_velocity = player.get_velocity()
	
	# 預測0.5秒後的位置
	var prediction_time = 0.5
	return player.global_position + player_velocity * prediction_time

func _predict_player_movement_learned() -> Vector2:
	"""基於學習模式的玩家移動預測"""
	if _player_movement_pattern.size() < 3:
		return player.global_position
	
	# 計算平均移動方向
	var average_movement = Vector2.ZERO
	for movement in _player_movement_pattern:
		average_movement += movement
	
	average_movement = average_movement / _player_movement_pattern.size()
	
	# 預測位置
	var prediction_time = 0.5
	return player.global_position + average_movement * prediction_time

func _choose_attack_strategy() -> String:
	"""根據適應等級選擇攻擊策略"""
	if _adaptation_level > 0.5:
		return "aggressive"  # 積極攻擊
	elif _adaptation_level < -0.5:
		return "defensive"   # 防禦性攻擊
	else:
		return "balanced"    # 平衡攻擊

func _execute_tracker_attack() -> bool:
	"""執行追蹤型敵人攻擊，返回是否成功"""
	if player == null:
		return false
	
	var player_health = player.find_child("HealthComponent")
	if player_health != null and player_health.has_method("take_damage"):
		player_health.take_damage(enemy_data.damage)
		
		# 播放攻擊音效
		if enemy_data != null and enemy_data.attack_sound != null:
			enemy.play_sound(enemy_data.attack_sound)
		
		return true
	
	return false

func _should_tactical_retreat() -> bool:
	"""判斷是否需要戰術撤退"""
	if player == null:
		return false
	
	var distance_to_player = enemy.global_position.distance_to(player.global_position)
	
	# 如果玩家太近，需要撤退
	if distance_to_player < enemy_data.attack_range * 0.5:
		return true
	
	# 如果生命值太低，需要撤退
	var health_component = enemy.find_child("HealthComponent")
	if health_component != null and health_component.has_method("get_health_percentage"):
		var health_percentage = health_component.get_health_percentage()
		if health_percentage < 0.3:  # 生命值低於30%
			return true
	
	return false

func _start_tactical_retreat() -> void:
	"""開始戰術撤退"""
	_tactical_retreat_timer = 0.0
	change_state(State.RETREAT)

func _update_retreat_state(_delta: float) -> void:
	# 撤退狀態：遠離玩家
	if player != null:
		var direction = (enemy.global_position - player.global_position).normalized()
		enemy.velocity = direction * enemy_data.move_speed * 0.8

func _update_stunned_state(_delta: float) -> void:
	# 眩暈狀態：無法行動
	enemy.velocity = Vector2.ZERO
	
	# 眩暈時間結束後回到閒置狀態
	if state_timer > 2.0:
		change_state(State.IDLE)

# 公共方法
func set_stunned(duration: float = 2.0) -> void:
	change_state(State.STUNNED)
	state_timer = -duration  # 負值表示眩暈時間

func get_current_state() -> State:
	return current_state

func is_in_attack_range() -> bool:
	if player == null:
		return false
	return enemy.global_position.distance_to(player.global_position) <= enemy_data.attack_range

func get_last_attack_time() -> float:
	return last_attack_time

func _update_group_behavior(_delta: float) -> void:
	# 尋找附近的敵人
	_find_nearby_enemies()
	
	# 感知環境
	_perceive_environment()
	
	# 學習玩家行為
	_learn_player_behavior()
	
	# 計算避免擁擠的力
	_calculate_avoidance_force()
	
	# 計算環境避免力
	_calculate_environment_avoidance_force()
	
	# 應用群體行為到移動
	_apply_group_behavior_to_movement()

func _find_nearby_enemies() -> void:
	_nearby_enemies.clear()
	
	# 尋找附近的敵人（排除自己）
	var enemies = get_tree().get_nodes_in_group("enemy_group")
	for other_enemy in enemies:
		if other_enemy != enemy:
			var distance = enemy.global_position.distance_to(other_enemy.global_position)
			if distance < 80.0:  # 群體行為範圍
				_nearby_enemies.append(other_enemy)

func _calculate_avoidance_force() -> void:
	_avoidance_force = Vector2.ZERO
	
	for other_enemy in _nearby_enemies:
		var direction = (enemy.global_position - other_enemy.global_position).normalized()
		var distance = enemy.global_position.distance_to(other_enemy.global_position)
		var force_strength = 1.0 / max(distance, 1.0)  # 距離越近，力越大
		_avoidance_force += direction * force_strength * 50.0  # 避免擁擠的力

func _perceive_environment() -> void:
	"""感知環境中的障礙物和危險"""
	if not _environment_awareness:
		return
	
	# 尋找障礙物
	_find_obstacles()
	
	# 尋找環境危險
	_find_environment_hazards()
	
	# 尋找路徑點
	_find_pathfinding_nodes()

func _find_obstacles() -> void:
	"""尋找附近的障礙物"""
	_obstacles.clear()
	
	# 尋找靜態障礙物
	var static_bodies = get_tree().get_nodes_in_group("obstacle_group")
	for obstacle in static_bodies:
		if obstacle != enemy:
			var distance = enemy.global_position.distance_to(obstacle.global_position)
			if distance < 120.0:  # 障礙物感知範圍
				_obstacles.append(obstacle)

func _find_environment_hazards() -> void:
	"""尋找環境危險"""
	_environment_hazards.clear()
	
	# 尋找環境危險（如火、陷阱等）
	var hazards = get_tree().get_nodes_in_group("hazard_group")
	for hazard in hazards:
		var distance = enemy.global_position.distance_to(hazard.global_position)
		if distance < 150.0:  # 危險感知範圍
			_environment_hazards.append(hazard)

func _find_pathfinding_nodes() -> void:
	"""尋找路徑點"""
	_pathfinding_nodes.clear()
	
	# 尋找路徑點（如檢查點、安全區域等）
	var path_nodes = get_tree().get_nodes_in_group("pathfinding_group")
	for path_node in path_nodes:
		var distance = enemy.global_position.distance_to(path_node.global_position)
		if distance < 200.0:  # 路徑點感知範圍
			_pathfinding_nodes.append(path_node)

func _calculate_environment_avoidance_force() -> void:
	"""計算環境避免力"""
	var environment_force = Vector2.ZERO
	
	# 避免障礙物
	for obstacle in _obstacles:
		var direction = (enemy.global_position - obstacle.global_position).normalized()
		var distance = enemy.global_position.distance_to(obstacle.global_position)
		var force_strength = 1.0 / max(distance, 1.0) * 80.0  # 障礙物避免力
		environment_force += direction * force_strength
	
	# 避免環境危險
	for hazard in _environment_hazards:
		var direction = (enemy.global_position - hazard.global_position).normalized()
		var distance = enemy.global_position.distance_to(hazard.global_position)
		var force_strength = 1.0 / max(distance, 1.0) * 120.0  # 危險避免力
		environment_force += direction * force_strength
	
	# 將環境避免力添加到總避免力中
	_avoidance_force += environment_force

func _apply_group_behavior_to_movement() -> void:
	# 在追蹤和巡邏狀態下應用群體行為
	if current_state == State.CHASE or current_state == State.PATROL:
		# 將避免擁擠的力添加到速度中
		enemy.velocity += _avoidance_force * get_process_delta_time()
		
		# 限制最大速度
		if enemy.velocity.length() > enemy_data.move_speed:
			enemy.velocity = enemy.velocity.normalized() * enemy_data.move_speed

func _is_path_blocked(direction: Vector2) -> bool:
	"""檢查路徑是否被阻擋"""
	var ray_length = 50.0
	var ray_end = enemy.global_position + direction * ray_length
	
	# 簡單的射線檢測（可以改進為更精確的物理檢測）
	for obstacle in _obstacles:
		var obstacle_pos = obstacle.global_position
		var distance_to_ray = _point_to_line_distance(obstacle_pos, enemy.global_position, ray_end)
		
		if distance_to_ray < 20.0:  # 如果障礙物太靠近射線
			return true
	
	return false

func _find_alternative_path() -> Vector2:
	"""尋找替代路徑"""
	if player == null:
		return Vector2.ZERO
	
	# 嘗試左右兩個方向
	var base_direction = enemy.global_position.direction_to(player.global_position)
	var left_direction = base_direction.rotated(PI / 4)  # 左轉45度
	var right_direction = base_direction.rotated(-PI / 4)  # 右轉45度
	
	# 檢查哪個方向沒有被阻擋
	if not _is_path_blocked(left_direction):
		return left_direction
	elif not _is_path_blocked(right_direction):
		return right_direction
	else:
		# 如果都被阻擋，嘗試更極端的角度
		var extreme_left = base_direction.rotated(PI / 2)
		var extreme_right = base_direction.rotated(-PI / 2)
		
		if not _is_path_blocked(extreme_left):
			return extreme_left
		elif not _is_path_blocked(extreme_right):
			return extreme_right
		else:
			# 如果所有方向都被阻擋，隨機選擇
			return base_direction.rotated(randf_range(-PI, PI))

func _point_to_line_distance(point: Vector2, line_start: Vector2, line_end: Vector2) -> float:
	"""計算點到線段的距離"""
	var line_vec = line_end - line_start
	var point_vec = point - line_start
	
	var line_length = line_vec.length_squared()
	if line_length == 0:
		return point_vec.length()
	
	var t = max(0, min(1, point_vec.dot(line_vec) / line_length))
	var projection = line_start + t * line_vec
	
	return point.distance_to(projection)

func _learn_player_behavior() -> void:
	"""學習和適應玩家行為"""
	if player == null:
		return
	
	# 記錄玩家位置變化
	var current_player_pos = player.global_position
	if _last_player_position != Vector2.ZERO:
		var movement = current_player_pos - _last_player_position
		_player_movement_pattern.append(movement)
		
		# 保持最近的10個移動記錄
		if _player_movement_pattern.size() > 10:
			_player_movement_pattern.pop_front()
	
	_last_player_position = current_player_pos
	
	# 更新適應等級
	_update_adaptation_level()

func _update_adaptation_level() -> void:
	"""更新適應等級"""
	var total_attacks = _successful_attacks + _failed_attacks
	if total_attacks > 0:
		var success_rate = float(_successful_attacks) / float(total_attacks)
		_adaptation_level = success_rate * 2.0 - 1.0  # 範圍：-1.0 到 1.0
	else:
		_adaptation_level = 0.0
