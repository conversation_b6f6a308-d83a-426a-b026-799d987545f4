extends "res://scripts/enemies/enemy.gd"
class_name ChargerEnemy

## 衝鋒型敵人
## 會快速衝向玩家進行近戰攻擊
## 行為邏輯由 EnemyAIStateMachine 控制

@export var charge_speed: float = 400.0
@export var charge_duration: float = 1.0
@export var charge_damage: float = 35.0
@export var stun_duration: float = 1.5
@export var charge_warning_time: float = 0.5

var _is_attacking: bool = false

# 視覺效果
@export var charge_trail_effect: PackedScene
@export var charge_warning_effect: PackedScene
@export var charge_impact_effect: PackedScene

func _ready() -> void:
	super._ready()
	
	# 設置衝鋒型敵人的特定屬性
	if enemy_data != null:
		enemy_data.enemy_type = EnemyData.EnemyType.CHARGER
		enemy_data.attack_range = 80.0
		enemy_data.detection_range = 700.0 # 擴大偵測範圍
		enemy_data.move_speed = 250.0

# 由 EnemyAIStateMachine 在 ATTACK 狀態時調用
func _execute_charger_attack() -> void:
	if _is_attacking or _player == null:
		return
	
	_is_attacking = true
	
	# --- 衝鋒警告 ---
	var charge_direction = global_position.direction_to(_player.global_position)
	velocity = Vector2.ZERO
	rotation = charge_direction.angle()
	_create_charge_warning_effect(charge_direction)
	if enemy_data != null and enemy_data.attack_sound != null:
		play_sound(enemy_data.attack_sound)
	
	# 等待警告時間，確保節點仍在樹上
	if not is_inside_tree():
		_is_attacking = false
		return
	var warn_tree := get_tree()
	if warn_tree == null:
		_is_attacking = false
		return
	await warn_tree.create_timer(charge_warning_time).timeout

	# --- 開始衝鋒 ---
	velocity = charge_direction * charge_speed
	_create_charge_trail_effect(charge_direction)
	
	var charge_timer = 0.0
	while charge_timer < charge_duration:
		# 若節點已不在樹上，提前結束
		if not is_inside_tree():
			break
		charge_timer += get_physics_process_delta_time()

		# 檢查碰撞
		if _player != null and global_position.distance_to(_player.global_position) <= enemy_data.attack_range:
			_on_charge_hit_player()
			break

		if get_slide_collision_count() > 0:
			_on_charge_hit_wall()
			break

		var tree := get_tree()
		if not tree:
			break
		await tree.physics_frame

	# --- 結束衝鋒 ---
	velocity = Vector2.ZERO
	if _ai_state_machine != null:
		_ai_state_machine.set_stunned(stun_duration)
	
	_is_attacking = false

func _on_charge_hit_player() -> void:
	if _player == null: return
	var player_health = _player.find_child("HealthComponent")
	if player_health != null and player_health.has_method("take_damage"):
		player_health.take_damage(charge_damage)

	# 播放擊中特效
	var vfx_manager = get_node_or_null("/root/VFXManager")
	if vfx_manager:
		vfx_manager.play_hit_effect(_player.global_position)

	_create_charge_impact_effect()
	if enemy_data != null and enemy_data.attack_sound != null:
		play_sound(enemy_data.attack_sound)

func _on_charge_hit_wall() -> void:
	_create_charge_impact_effect()
	if enemy_data != null and enemy_data.move_sound != null:
		play_sound(enemy_data.move_sound)

func _create_charge_warning_effect(direction: Vector2) -> void:
	if charge_warning_effect == null: return
	var effect = charge_warning_effect.instantiate()
	var tree := get_tree()
	if tree and tree.current_scene:
		tree.current_scene.add_child(effect)
	elif tree and tree.root:
		tree.root.add_child(effect)
	else:
		add_child(effect)
	effect.global_position = global_position
	effect.rotation = direction.angle()

func _create_charge_trail_effect(direction: Vector2) -> void:
	if charge_trail_effect == null: return
	var effect = charge_trail_effect.instantiate()
	add_child(effect) # 作為子節點跟隨移動
	effect.rotation = direction.angle()

func _create_charge_impact_effect() -> void:
	if charge_impact_effect == null: return
	var effect = charge_impact_effect.instantiate()
	var tree := get_tree()
	if tree and tree.current_scene:
		tree.current_scene.add_child(effect)
	elif tree and tree.root:
		tree.root.add_child(effect)
	else:
		add_child(effect)
	effect.global_position = global_position
