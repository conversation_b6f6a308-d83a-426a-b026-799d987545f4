extends "res://scripts/enemies/enemy.gd"
class_name TrackerEnemy

## 追蹤型敵人
## 會直接追蹤玩家並進行近戰攻擊
## 行為邏輯由 EnemyAIStateMachine 控制

@export var tracking_speed: float = 180.0  # 追蹤速度

func _ready() -> void:
	super._ready()
	
	# 設置追蹤型敵人的特定屬性
	if enemy_data != null:
		enemy_data.enemy_type = EnemyData.EnemyType.TRACKER
		enemy_data.attack_range = 40.0
		enemy_data.detection_range = 800.0  # 擴大偵測範圍並交由狀態機使用
		enemy_data.move_speed = tracking_speed

func _execute_tracker_attack() -> void:
	# 實現追蹤型敵人的特定攻擊
	# 這個方法由 EnemyAIStateMachine 在 ATTACK 狀態時調用
	if _player == null:
		return
	
	var player_health = _player.find_child("HealthComponent")
	if player_health != null and player_health.has_method("take_damage"):
		player_health.take_damage(enemy_data.damage)
		
		# 播放攻擊音效
		if enemy_data != null and enemy_data.attack_sound != null:
			play_sound(enemy_data.attack_sound)
		
		# 攻擊後短暫停頓
		velocity = Vector2.ZERO
		await get_tree().create_timer(0.3).timeout
