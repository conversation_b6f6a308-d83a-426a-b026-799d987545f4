extends "res://scripts/enemies/enemy.gd"
class_name ShooterEnemy

## 射擊型敵人
## 會保持距離並發射投射物攻擊玩家
## 行為邏輯由 EnemyAIStateMachine 控制

@export var projectile_scene: PackedScene
@export var attack_range_min: float = 150.0  # 最小攻擊距離
@export var attack_range_max: float = 250.0  # 最大攻擊距離
@export var projectile_speed: float = 250.0
@export var projectile_damage: float = 20.0
@export var burst_count: int = 3  # 連發數量
@export var burst_delay: float = 0.1  # 連發間隔

var _is_attacking: bool = false

func _ready() -> void:
	super._ready()
	
	# 設置射擊型敵人的特定屬性
	if enemy_data != null:
		enemy_data.enemy_type = EnemyData.EnemyType.SHOOTER
		enemy_data.attack_range = attack_range_max
		enemy_data.detection_range = 900.0 # 擴大偵測範圍

# 由 EnemyAIStateMachine 在 ATTACK 狀態時重複調用
func _execute_shooter_attack() -> void:
	if _is_attacking:
		return

	_is_attacking = true
	
	# 播放攻擊音效
	if enemy_data != null and enemy_data.attack_sound != null:
		play_sound(enemy_data.attack_sound)

	# 使用計時器處理連發
	for i in burst_count:
		_fire_projectile()
		if i < burst_count - 1:
			await get_tree().create_timer(burst_delay).timeout
	
	_is_attacking = false

func _fire_projectile() -> void:
	if _player == null or projectile_scene == null:
		return

	var direction = global_position.direction_to(_player.global_position)

	var projectile = NodePoolManager.acquire(projectile_scene)
	# 設置朝向與速度屬性需在加到場景前或後皆可
	if projectile.has_method("initialize"):
		projectile.initialize(direction, projectile_speed)
	if projectile.has_method("set_projectile_properties"):
		projectile.set_projectile_properties(projectile_speed, projectile_damage)
	projectile.global_position = global_position + direction * 20.0

	var tree := get_tree()
	if tree and tree.current_scene:
		tree.current_scene.add_child(projectile)
	elif tree and tree.root:
		tree.root.add_child(projectile)

# 公共方法
func set_projectile_scene(new_scene: PackedScene) -> void:
	projectile_scene = new_scene

func set_attack_range(min_range: float, max_range: float) -> void:
	attack_range_min = min_range
	attack_range_max = max_range
	if enemy_data != null:
		enemy_data.attack_range = max_range

func set_burst_properties(count: int, delay: float) -> void:
	burst_count = count
	burst_delay = delay
