extends CharacterBody2D

var _pending_pool_return := false

## 基礎敵人類，支援多種類型和AI行為

@export var enemy_data: EnemyData
@export var experience_orb_scene: PackedScene

var _player: Node2D
var _ai_state_machine: EnemyAIStateMachine
var _health_component: Node
var _health_bar: Node

func _ready() -> void:
	# 如果沒有設置敵人類型數據，使用預設值
	if enemy_data == null:
		enemy_data = EnemyData.get_default_data(EnemyData.EnemyType.TRACKER)
	
	# 尋找玩家節點
	_player = get_tree().get_first_node_in_group("player_group")
	
	# 初始化AI狀態機
	_initialize_ai_state_machine()
	
	# 連接生命值組件
	_connect_health_components()
	
	# 設置敵人屬性
	_setup_enemy_properties()

func _initialize_ai_state_machine() -> void:
	# 創建AI狀態機
	_ai_state_machine = EnemyAIStateMachine.new()
	_ai_state_machine.enemy = self
	_ai_state_machine.enemy_data = enemy_data
	add_child(_ai_state_machine)
	
	# 連接狀態變化信號
	_ai_state_machine.state_changed.connect(_on_ai_state_changed)

func _connect_health_components() -> void:
	_health_component = find_child("HealthComponent")
	_health_bar = find_child("HealthBar")
	
	if _health_component != null and _health_bar != null:
		_health_bar.connect_to_health_component(_health_component)
	
	# 連接死亡信號
	if _health_component != null:
		# Check if it's a BossHealthComponent
		if _health_component is BossHealthComponent:
			if not _health_component.boss_defeated.is_connected(_on_health_component_died):
				_health_component.boss_defeated.connect(_on_health_component_died)
		else:
			# Assume it's a generic HealthComponent with a 'died' signal
			if not _health_component.died.is_connected(_on_health_component_died):
				_health_component.died.connect(_on_health_component_died)

func _setup_enemy_properties() -> void:
	if enemy_data == null:
		return
	
	# 設置生命值
	if _health_component != null and _health_component.has_method("set_max_health"):
		_health_component.set_max_health(enemy_data.max_health)
	
	# 設置移動速度（通過AI狀態機控制）
	# 設置傷害值（通過AI狀態機控制）

func _physics_process(_delta: float) -> void:
	# AI狀態機會自動處理移動和攻擊邏輯
	# 這裡只需要處理物理移動
	move_and_slide()
	
	# 檢查與玩家的碰撞（用於近戰攻擊）
	_check_player_collision()

func _check_player_collision() -> void:
	# 檢查是否與玩家碰撞並造成傷害
	if _ai_state_machine == null or not _ai_state_machine.is_in_attack_range():
		return
	
	var current_time = Time.get_ticks_msec() / 1000.0
	var last_attack_time = 0.0
	if _ai_state_machine != null and _ai_state_machine.has_method("get_last_attack_time"):
		last_attack_time = _ai_state_machine.get_last_attack_time()
	
	if current_time - last_attack_time < enemy_data.damage_cooldown:
		return
	
	# 檢查碰撞
	for i in get_slide_collision_count():
		var collision: KinematicCollision2D = get_slide_collision(i)
		var collider: Node2D = collision.get_collider()
		
		if collider != null and collider.is_in_group("player_group"):
			var player_health: Node = collider.find_child("HealthComponent")
			if player_health != null and player_health.has_method("take_damage"):
				player_health.take_damage(enemy_data.damage)
				# 更新最後攻擊時間
				if _ai_state_machine != null:
					_ai_state_machine.last_attack_time = current_time
				break


func _on_ai_state_changed(_old_state: EnemyAIStateMachine.State, new_state: EnemyAIStateMachine.State) -> void:
	# 根據AI狀態變化更新敵人行為
	match new_state:
		EnemyAIStateMachine.State.IDLE:
			# 閒置狀態：停止移動
			velocity = Vector2.ZERO
			
		EnemyAIStateMachine.State.PATROL:
			# 巡邏狀態：AI會自動設置移動方向
			pass
			
		EnemyAIStateMachine.State.CHASE:
			# 追蹤狀態：AI會自動設置移動方向
			pass
			
		EnemyAIStateMachine.State.ATTACK:
			# 攻擊狀態：停止移動，執行攻擊
			velocity = Vector2.ZERO
			_execute_attack()
			
		EnemyAIStateMachine.State.RETREAT:
			# 撤退狀態：AI會自動設置移動方向
			pass
			
		EnemyAIStateMachine.State.STUNNED:
			# 眩暈狀態：停止移動
			velocity = Vector2.ZERO

func _execute_attack() -> void:
	if enemy_data == null:
		return
	
	# 根據敵人類型執行不同攻擊
	match enemy_data.enemy_type:
		EnemyData.EnemyType.TRACKER:
			_execute_tracker_attack()
		EnemyData.EnemyType.SHOOTER:
			_execute_shooter_attack()
		EnemyData.EnemyType.CHARGER:
			_execute_charger_attack()

func _execute_tracker_attack() -> void:
	# 追蹤型敵人：近戰攻擊
	if _player != null:
		var player_health = _player.find_child("HealthComponent")
		if player_health != null and player_health.has_method("take_damage"):
			player_health.take_damage(enemy_data.damage)
			
			# 播放攻擊音效
			if enemy_data.attack_sound != null:
				var am = get_node_or_null("/root/AudioManager")
				if am:
					am.play_sfx(enemy_data.attack_sound)
				else:
					play_sound(enemy_data.attack_sound)

func _execute_shooter_attack() -> void:
	# 射擊型敵人：發射投射物
	if _player != null:
		# 這裡會由具體的射擊型敵人腳本實現
		pass

func _execute_charger_attack() -> void:
	# 衝鋒型敵人：衝鋒攻擊
	if _player != null:
		var player_health = _player.find_child("HealthComponent")
		if player_health != null and player_health.has_method("take_damage"):
			player_health.take_damage(enemy_data.damage)
			
			# 播放攻擊音效
			if enemy_data.attack_sound != null:
				var am = get_node_or_null("/root/AudioManager")
				if am:
					am.play_sfx(enemy_data.attack_sound)
				else:
					play_sound(enemy_data.attack_sound)

			# 衝鋒後眩暈
			if _ai_state_machine != null:
				_ai_state_machine.set_stunned(1.5)

func play_sound(audio_stream: AudioStream) -> void:
	# 已遷移到 AudioManager，這裡保留後備方案
	var am = get_node_or_null("/root/AudioManager")
	if am:
		am.play_sfx(audio_stream)
		return
	var audio_player = AudioStreamPlayer.new()
	audio_player.stream = audio_stream
	audio_player.volume_db = -10.0
	add_child(audio_player)
	audio_player.play()
	audio_player.finished.connect(func(): audio_player.queue_free())

@export var meta_currency_reward: int = 5 # Amount of currency to grant


func _on_health_component_died() -> void:
	# Grant meta-currency to the player
	SaveManager.add_currency(meta_currency_reward)

	# 播放死亡音效
	if enemy_data != null and enemy_data.death_sound != null:
		var am = get_node_or_null("/root/AudioManager")
		if am:
			am.play_sfx(enemy_data.death_sound)
		else:
			play_sound(enemy_data.death_sound)

	# 創建死亡粒子效果
	if enemy_data != null and enemy_data.particle_effect != null:
		_create_death_effect()
	
	# 保存位置並生成經驗球
	var spawn_position = global_position
	call_deferred("_spawn_experience_orb", spawn_position)
	
	# 延遲回收到節點池，讓音效和效果播放完成
	call_deferred("_return_to_pool")


func _create_death_effect() -> void:
	if enemy_data.particle_effect == null:
		return
	
	var effect = enemy_data.particle_effect.instantiate()
	effect.global_position = global_position
	
	# 添加到場景
	var current_scene = get_tree().current_scene
	if current_scene != null:
		current_scene.add_child(effect)

func _spawn_experience_orb(spawn_position: Vector2) -> void:
	if experience_orb_scene == null:
		# 嘗試載入經驗球場景
		experience_orb_scene = preload("res://scenes/pickups/experience_orb.tscn")
	
	if experience_orb_scene != null:
		var experience_orb = experience_orb_scene.instantiate()
		experience_orb.global_position = spawn_position
		
		# 設置經驗值
		if enemy_data != null and experience_orb.has_method("set_experience_value"):
			experience_orb.set_experience_value(enemy_data.experience_value)
		
		# 添加到場景
		var current_scene = get_tree().current_scene
		if current_scene != null:
			current_scene.add_child(experience_orb)
		else:
			# 備用方案：嘗試添加到根節點
			var root = get_tree().root
			if root != null:
				root.add_child(experience_orb)
			else:
				push_error("無法添加經驗球：場景樹不可用")

# 公共方法
func set_enemy_data(new_data: EnemyData) -> void:
	enemy_data = new_data
	if _ai_state_machine != null:
		_ai_state_machine.enemy_data = new_data
	_setup_enemy_properties()

func get_enemy_type() -> EnemyData.EnemyType:
	if enemy_data != null:
		return enemy_data.enemy_type
	return EnemyData.EnemyType.TRACKER

func set_stunned(duration: float = 2.0) -> void:
	if _ai_state_machine != null:
		_ai_state_machine.set_stunned(duration)

# 節點池支援：被回收與取用時的重置
func on_returned_to_pool() -> void:
	_pending_pool_return = false
	velocity = Vector2.ZERO
	set_process(false)
	set_physics_process(false)
	visible = false

func on_taken_from_pool() -> void:
	# 重置生命、AI 等外部系統通常由 Spawner 設置 enemy_data 後生效
	set_process(true)
	set_physics_process(true)
	visible = true
	# 確保生命值重置
	var hc: Node = find_child("HealthComponent")
	if hc:
		# HealthComponent 具有 max_health / current_health 變數
		hc.current_health = hc.max_health

func _return_to_pool() -> void:
	if _pending_pool_return:
		return
	_pending_pool_return = true
	var pool := get_tree().root.get_node_or_null("NodePoolManager")
	if pool:
		pool.return_node(self)
	else:
		queue_free()
