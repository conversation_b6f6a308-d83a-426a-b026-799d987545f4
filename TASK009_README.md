# TASK009: 多樣化的敵人與AI

## 概述
本任務實現了多種類型的敵人，每種都有獨特的行為模式和攻擊方式，增加了遊戲的挑戰性和趣味性。

## 實現的功能

### 1. 敵人類型系統
- **追蹤型敵人 (Tracker)**: 紅色敵人，會主動追蹤玩家並進行近戰攻擊
- **射擊型敵人 (Shooter)**: 藍色敵人，會保持距離並發射投射物攻擊玩家
- **衝鋒型敵人 (Charger)**: 橙色敵人，會快速衝向玩家進行衝鋒攻擊

### 2. AI行為模式
- **追蹤型**: 簡單的追蹤AI，敵人會朝著玩家的方向移動
- **射擊型**: 遠程攻擊邏輯，敵人會保持理想射擊距離
- **衝鋒型**: 衝鋒攻擊模式，包含警告、衝鋒、眩暈等階段

### 3. 視覺效果
- 每種敵人類型都有獨特的顏色和形狀
- 衝鋒型敵人有完整的視覺效果系統：
  - 衝鋒前警告效果（黃色脈衝）
  - 衝鋒時拖尾效果
  - 撞擊時的震動和粒子效果

### 4. 攻擊系統
- **追蹤型**: 近戰攻擊，造成持續傷害
- **射擊型**: 連發投射物攻擊，可調整射程和連發數量
- **衝鋒型**: 高傷害衝鋒攻擊，有冷卻時間和眩暈效果

## 文件結構

### 敵人腳本
- `scripts/enemies/enemy.gd` - 基礎敵人類
- `scripts/enemies/shooter_enemy.gd` - 射擊型敵人
- `scripts/enemies/charger_enemy.gd` - 衝鋒型敵人
- `scripts/enemies/enemy_ai_state_machine.gd` - AI狀態機

### 敵人場景
- `scenes/enemies/tracker_enemy.tscn` - 追蹤型敵人場景
- `scenes/enemies/shooter_enemy.tscn` - 射擊型敵人場景
- `scenes/enemies/charger_enemy.tscn` - 衝鋒型敵人場景

### 敵人資源
- `resources/enemies/tracker_enemy.tres` - 追蹤型敵人數據
- `resources/enemies/shooter_enemy.tres` - 射擊型敵人數據
- `resources/enemies/charger_enemy.tres` - 衝鋒型敵人數據

### 效果系統
- `scenes/effects/charge_warning_effect.tscn` - 衝鋒警告效果
- `scenes/effects/charge_trail_effect.tscn` - 衝鋒拖尾效果
- `scenes/effects/charge_impact_effect.tscn` - 衝鋒撞擊效果

### 投射物系統
- `scenes/weapons/enemy_projectile.tscn` - 敵人投射物
- `scripts/weapons/enemy_projectile.gd` - 敵人投射物腳本

## 使用方法

### 1. 測試敵人系統
運行 `scenes/test_scene.tscn` 來測試多樣化的敵人系統：

```bash
# 在Godot編輯器中打開test_scene.tscn
# 或者通過命令行運行
godot --path . --headless --quit-after 10
```

### 2. 自定義敵人
可以通過修改敵人資源文件來調整敵人屬性：

```gdscript
# 在EnemyData資源中調整
enemy_data.max_health = 100.0      # 生命值
enemy_data.move_speed = 200.0      # 移動速度
enemy_data.damage = 25.0           # 傷害值
enemy_data.detection_range = 400.0 # 偵測範圍
enemy_data.attack_range = 50.0     # 攻擊範圍
```

### 3. 添加新的敵人類型
1. 創建新的敵人腳本，繼承自 `enemy.gd`
2. 在 `EnemyData.EnemyType` 枚舉中添加新類型
3. 創建對應的場景和資源文件
4. 在 `enemy_spawner.gd` 中添加生成邏輯

## 技術特點

### 1. 狀態機設計
使用狀態機模式管理敵人的不同行為狀態：
- IDLE: 閒置狀態
- PATROL: 巡邏狀態
- CHASE: 追蹤狀態
- ATTACK: 攻擊狀態
- RETREAT: 撤退狀態
- STUNNED: 眩暈狀態

### 2. 組件化設計
敵人系統採用組件化設計，每個功能模組獨立：
- 生命值組件 (HealthComponent)
- AI狀態機組件 (EnemyAIStateMachine)
- 視覺效果組件 (各種Effect)

### 3. 資源驅動
敵人屬性通過資源文件配置，便於調整和平衡：
- 生命值、移動速度、傷害值等
- 偵測範圍、攻擊範圍等AI參數
- 視覺效果和音效資源

## 平衡性考慮

### 1. 難度曲線
- **追蹤型**: 基礎敵人，適合新手
- **射擊型**: 中等難度，需要玩家機動性
- **衝鋒型**: 高難度，需要玩家反應和策略

### 2. 數值平衡
- 生命值: 追蹤型(80) < 射擊型(60) < 衝鋒型(120)
- 移動速度: 射擊型(100) < 追蹤型(180) < 衝鋒型(250)
- 傷害值: 追蹤型(15) < 射擊型(20) < 衝鋒型(25)

### 3. 冷卻時間
- 追蹤型: 1.2秒攻擊冷卻
- 射擊型: 2.5秒攻擊冷卻
- 衝鋒型: 3.0秒攻擊冷卻

## 未來改進

### 1. 音效系統
- 為每種敵人類型添加獨特的音效
- 實現3D音效定位
- 添加環境音效

### 2. 動畫系統
- 實現敵人行走、攻擊、死亡動畫
- 添加過渡動畫和混合
- 支持動畫事件系統

### 3. AI改進
- 實現更複雜的尋路算法
- 添加群體行為和協調
- 實現動態難度調整

### 4. 視覺效果
- 添加更多粒子效果
- 實現動態光照和陰影
- 支持後處理效果

## 測試建議

### 1. 功能測試
- 測試每種敵人的基本行為
- 驗證AI狀態轉換
- 檢查碰撞檢測和傷害系統

### 2. 性能測試
- 測試大量敵人同時存在時的性能
- 檢查記憶體使用情況
- 驗證粒子效果的性能影響

### 3. 平衡性測試
- 測試不同敵人組合的難度
- 驗證數值平衡
- 檢查遊戲節奏

## 總結

TASK009成功實現了多樣化的敵人與AI系統，為遊戲增加了豐富的挑戰性和策略深度。通過狀態機設計、組件化架構和資源驅動配置，系統具有良好的可擴展性和可維護性。每種敵人類型都有獨特的行為模式和視覺效果，為玩家提供了多樣化的遊戲體驗。
