# Wavebreaker

*破浪者 — 在無盡的敵人波濤中生存、進化、尋求突破。*

## 1. 遊戲概覽

**Wavebreaker** 是一款上帝視角的競技場生存遊戲 (Survivors-like)，融合了 Roguelite 元素。玩家將選擇一名角色，在充滿敵意的星球上對抗一波又一波不斷增強的怪物。透過擊敗敵人獲取經驗、升級並解鎖強大的武器與技能組合，玩家的目標是在無情的圍攻下盡可能地生存下去，並最終挑戰強大的頭目，打破宿命的循環。

*   **類型:** Arena Survival, Action, Roguelite
*   **平台:** PC (Windows, macOS, Linux)
*   **引擎:** Godot Engine 4.4 (使用 GL Compatibility 渲染器以獲得最大相容性)
*   **美術風格:** 乾淨且高對比度的Q版扁平化卡通風格 (Chibi Flat Cartoon Style)。這種風格辨識度高、親和力強，給人一種輕鬆、可愛且帶點幻想色彩的感覺。

## 2. 核心玩法循環

遊戲的核心圍繞著一個緊張刺激的循環：

1.  **生存 (Survive):** 玩家在場景中自動攻擊，需要專注於移動，躲避密集的敵人和彈幕。
2.  **收集 (Collect):** 擊敗敵人會掉落經驗水晶。玩家需要主動靠近以收集它們。
3.  **升級 (Level Up):** 收集足夠的經驗後，玩家會升級。遊戲暫停，並提供 3-4 個隨機的武器或技能選項供玩家選擇，這是構築 (Build) 策略的關鍵。
4.  **構築 (Build):** 玩家的選擇將塑造角色的戰鬥風格。武器會自動進化，技能則提供被動加成。玩家需要根據當前擁有的物品做出策略性選擇，以創造出強大的協同效應。
5.  **征服 (Conquer):** 在標準的敵人波次之間，會出現精英敵人或最終的頭目。擊敗它們是前進到下一階段或完成一局遊戲的目標。
6.  **死亡與重複 (Die & Repeat):** 當玩家的生命值歸零時，本局遊戲結束。玩家將根據本局表現獲得用於永久性升級的貨幣，然後帶著新的知識和更強的基礎屬性開始下一次挑戰。

## 3. 主要功能詳解

本節將作為 AI 開發的主要藍圖，定義了遊戲的關鍵系統。

### 功能區 1: 核心玩法系統

*   **玩家控制器 (Player Controller):**
    *   響應式八向移動。
    *   包含核心屬性：生命值 (Health), 護甲 (Armor), 移動速度 (Move Speed), 拾取範圍 (Pickup Range), 傷害加成 (Damage Bonus) 等。
*   **波次生成系統 (Wave Spawning System):**
    *   基於時間的腳本化波次系統，用於定義在遊戲的哪個時間點生成哪些敵人組合。
    *   系統應能隨著時間推移，動態增加敵人的數量和強度。
*   **武器與技能系統 (Weapon & Ability System):**
    *   **武器:** 自動觸發攻擊。可透過升級來提升傷害、範圍、發射物數量等。達到最高等級後可與特定技能融合成更強大的 "進化武器"。
    *   **技能:** 主要提供被動加成，如增加移動速度、減少冷卻時間、提供防禦選項等。
    *   所有武器和技能的數據都應由外部資源文件 (如 `Resource` 或 JSON) 定義，方便擴展。
*   **經驗與升級系統 (Experience & Leveling System):**
    *   敵人死亡時掉落經驗水晶。
    *   定義一個非線性的升級曲線，等級越高，所需經驗越多。
    *   升級時觸發遊戲暫停，並彈出升級選擇介面。
*   **敵人 AI (Enemy AI):**
    *   **集群敵人 (Swarmer):** 數量多，生命值低，只會直線追擊玩家。
    *   **遠程敵人 (Ranged):** 與玩家保持一定距離並發射彈幕。
    *   **精英敵人 (Elite):** 體型更大，生命值更高，可能擁有特殊技能（如衝鋒、範圍攻擊）。
*   **遊戲狀態管理 (Game State Management):**
    *   使用一個單例 (Singleton) 或場景樹根節點來管理整個遊戲的狀態：主選單 (MainMenu), 遊戲中 (InGame), 暫停 (Paused), 遊戲結束 (GameOver), 局外升級 (MetaUpgrade)。

### 功能區 2: 內容生成與持續性

*   **程序化地圖元素 (Procedural Map Elements):**
    *   雖然地圖邊界固定，但內部會隨機生成一些障礙物（如岩石、樹木），以增加每局遊戲的變化性。
*   **多樣化的敵人名冊 (Diverse Enemy Roster):**
    *   一個包含多種敵人的庫，每種敵人都有獨特的視覺和行為模式。
*   **可解鎖內容 (Unlockable Content):**
    *   **角色:** 每個角色擁有不同的初始武器和基礎屬性。
    *   **武器/技能:** 新的武器和技能可以透過完成特定成就來解鎖，並加入到升級時的隨機池中。
*   **局外成長系統 (Meta-Progression System):**
    *   玩家在每局遊戲結束後，可以花費獲得的貨幣來永久性地提升所有角色的基礎屬性（如初始生命值、金幣獲取率等）。

### 功能區 3: 技術與美術需求

*   **UI / UX:**
    *   **HUD:** 清晰顯示生命值、等級、經驗條、遊戲時間和當前武器/技能。
    *   **選單:** 主選單、暫停選單、遊戲結束統計、局外升級介面。
    *   **升級介面:** 在升級時清晰展示選項的圖標、名稱和描述。
*   **視覺特效 (VFX):**
    *   為武器攻擊、擊中效果、敵人死亡、拾取經驗、升級等關鍵事件製作 impactful 的視覺特效。
*   **音頻 (Audio):**
    *   為所有玩家動作、UI 交互、武器和敵人攻擊/死亡製作對應的音效 (SFX)。
    *   為遊戲的不同階段（如平靜期、激烈戰鬥、頭目戰）製作動態的背景音樂 (BGM)。
*   **性能優化 (Performance):**
    *   核心挑戰之一。需要能夠在同一螢幕上流暢地處理數百個敵人節點。將使用 `RenderingServer`、多線程和節點複用 (Node Pooling) 等技術進行優化。

## 4. 開發原則

*   **風格指南:** 所有 GDScript 代碼必須嚴格遵守 `docs/GODOT_STYLE_GUIDE.md` 中的規範。
*   **模組化設計:** 系統之間應低耦合。例如，武器系統不應直接依賴於特定的玩家場景。盡可能使用信號 (Signals) 和可注入的資源 (injectable Resources) 進行通信。
*   **AI 驅動開發:** 本 `README.md` 文件是專案的最高指示。所有開發（特別是透過 AI 助手）都應以此為藍圖，確保功能實現與設計意圖一致。對藍圖的任何重大修改都應先更新此文件。