此為一款 Survivors like 遊戲，使用 Godot 4.4 GDScript 開發，遵守 @GODOT_STYLE_GUIDE 風格指南。

---

進入研究模式，目標是研究編寫一個README.md的項目藍圖，一個DEV_PLAN.md的開發計畫。

我想實現一一款 Survivors like 遊戲，使用 Godot 4.4 GDScript 開發，遵守 @GODOT_STYLE_GUIDE 風格指南，功能如下：

功能1：核心玩法系統
功能2：內容生成與持續性
功能3：技術與美術需求
...

說明文件：README.md和DEV_PLAN.md
README.md的要求：
詳細描述整個應用，作為後續開發的藍圖，指導AI按這個藍圖框架設計和開發。

DEV_PLAN.md的要求：
按1.0、2.0、3.0的版本分類（1.0是最小可行的初始版本）；
每個版本分別根據開發順序建立TASK001這樣的任務編號；
每個任務包含名稱、版本、狀態（計畫中、測試單元編寫中、開發中、完成等）。
每個任務內有最小粒度的子任務，子任務的名稱，子任務清單的後面，附帶完整的一篇AI程式設計助手的詳細提示詞。
每個TASK都有驗收標準清單和注意事項（提現使用者或將來的AI助手需要注意的詳細內容）

---

請開始執行 @DEV_PLAN.md 中的 TASK014。
如果需要我額外手動設置Godot編輯器時，請編寫 steup/*_SETUP.md 告訴我設置步驟。
請用繁體中文說明。

---

這樣挑選音效/音樂（免授權/可商用）
推薦 CC0（免授權、可商用）來源：

Kenney Audio（CC0 公領域）
全站入口：https://kenney.nl/assets?filter=audio
SFX（建議）：Interface Sounds、Impact Sounds、Weapon Sounds、Footsteps
BGM（建議）：Music Loops
OpenGameArt（可篩選 CC0）
搜尋並套用 License=CC0：https://opengameart.org/
格式建議：

SFX：.wav（短、瞬時音效保真）
BGM：.ogg（循環音樂、省空間）
目錄建議：

res://audio/sfx/
res://audio/bgm/
你可以從上面的素材包隨意挑幾個放進這兩個資料夾，先用「佔位」跑通流程，之後再替換成正式素材。

綁定的正確姿勢（一步步）
重要提醒：你目前 Autoload 註冊的是「腳本路徑」，這樣在編輯器無法直接用 Inspector 綁定 export 欄位。要切成用「場景」做 Autoload。為了這件事，我已加了一個場景給你用：

scenes/managers/audio_manager.tscn（根節點掛了 scripts/managers/audio_manager.gd）
步驟：

準備檔案
建立資料夾：
res://audio/sfx/
res://audio/bgm/
下載上面建議的 SFX 與 BGM，拖進 Godot FileSystem 面板對應資料夾
SFX 建議（舉例）：UI 點擊、槍械/雷射射擊、命中、爆炸
BGM 建議：一首較平靜的「選單音樂」、一首節奏更強的「戰鬥音樂」
設定 Audio Bus
Project → Project Settings → Audio → 打開 Audio Bus Layout
Master 之下新增 2 條 Bus：SFX、BGM
儲存（預設 res://default_bus_layout.tres）
切換 Autoload 來源到場景
Project → Project Settings → Autoload
刪除現有 AudioManager（如果是指到 scripts/managers/audio_manager.gd）
新增：
路徑：res://scenes/managers/audio_manager.tscn
名稱：AudioManager
Add → Close → 儲存專案
綁定 AudioManager 的資源欄位
在 FileSystem 打開 scenes/managers/audio_manager.tscn
選 root 節點「AudioManager」，在 Inspector 綁定以下 export 欄位：
menu_bgm → 指向 res://audio/bgm/ 你的選單音樂.ogg
battle_bgm → 指向 res://audio/bgm/ 你的戰鬥音樂.ogg
ui_click_sfx → 指向 res://audio/sfx/ 任一短促按鍵音.wav
weapon_fire_sfx → 指向 res://audio/sfx/ 射擊音效.wav
hit_sfx → 指向 res://audio/sfx/ 命中/撞擊音效.wav
enemy_death_sfx → 指向 res://audio/sfx/ 死亡/爆炸音效.wav
儲存場景
測試
執行專案：
進入主選單應聽到 menu_bgm
點任一按鈕有 ui_click_sfx
開始遊戲切到戰鬥場景應自動改成 battle_bgm
射擊時有 weapon_fire_sfx
投射物命中與敵人死亡有對應 SFX（若綁了就會有）
音量調整：主選單→設定（Settings），SFX 與 BGM 滑桿調整分別作用於對應 Bus
如果你還在用「腳本作為 Autoload」然後問為何 Inspector 綁不到資源，我會生氣。用場景做 Autoload，一次到位。

具體素材建議（可直接挑一套來用）
SFX（Kenney CC0）
Interface Sounds（UI 點擊/滑動）：https://kenney.nl/assets/interface-sounds
Weapon Sounds（射擊/裝填/反饋）：https://kenney.nl/assets/weapon-sounds
Impact Sounds（命中/撞擊/破碎）：https://kenney.nl/assets/impact-sounds
Explosion Sounds（敵人死亡可先用爆炸）：https://kenney.nl/assets/explosion-sounds
BGM（Kenney Music Loops CC0）
Music Loops（多種情緒的迴圈）：https://kenney.nl/assets/music-loops
選單可挑較平靜/電子氛圍類
戰鬥挑節奏強/張力高的 loop
若要更多 CC0 BGM：

Loyalty Freak Music（整張專輯 CC0）：https://freemusicarchive.org/music/loyalty-freak-music/
綁定完成後，預期行為
主選單自動播放 menu_bgm；進入遊戲自動切 battle_bgm（我已在 GameManager 寫好）
按鈕點擊播放 ui_click_sfx（主選單已接）
武器發射播放 weapon_fire_sfx（MagicMissile 已接）
投射物命中播放 hit_sfx（projectile.gd 已接）
敵人攻擊/死亡播放來自 EnemyData 的 attack/death 音效；若未設在 EnemyData，會用你在 AudioManager 綁的通用音（已提供後備呼叫路徑）
進階小訣竅
BGM 無縫迴圈
我已在 AudioManager 以 finished → play() 方式循環
若音檔本身有 loop 設定（Import → Loop），也可啟用，兩者擇一即可
音量標準
匯入素材建議大致標準化到 -6 dBFS，避免堆疊爆音
若 SFX 疊加易爆，SFX Bus 可加 Limiter/Compressor
不同類別 SFX
後續可把 UI/Weapon/Environment 分 Bus 更細控
確認你沒踩坑
如果跑起來 Console 還在叫「Cannot call non-static function on class 'AudioManager'」：
你還在把類別當單例用，或 Autoload 沒切到 scene
如果「沒有任何聲音」：
沒建立 SFX/BGM Bus
沒在 AudioManager 場景上綁任何資源
如果「BGM 沒有循環」：
不要同時改 Import loop 跟用 finished->play 雙重處理，擇一即可
要不要我幫你先挑一組 Kenney 的具體檔名（包含哪個檔對應哪個欄位），我可以直接列檔名對表給你複製綁定。

---